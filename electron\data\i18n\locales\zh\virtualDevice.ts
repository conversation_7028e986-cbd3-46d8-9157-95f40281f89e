/**
 * 虚拟化设备中文语言包
 */
export default {
  menus: {
    analog: {
      name: "模拟量",
      desc: "模拟量参数",
    },
    digitalInput: {
      name: "开入量",
      desc: "开入量参数",
    },
    digitalOutput: {
      name: "开出量",
      desc: "开出量参数",
    },
    fault: {
      name: "故障量",
      desc: "故障量参数",
    },
    led: {
      name: "led参数",
      desc: "LED参数",
    },
    waveReplay: {
      name: "故障回放",
      desc: "故障录波回放",
    },
  },
  items: {
    analog: {
      name: "模拟量输入1",
      desc: "模拟量输入1",
    },
    digitalInput: {
      name: "开入量1",
      desc: "开入量1",
    },
    digitalOutput: {
      name: "开出量1",
      desc: "开出量1",
    },
    fault: {
      name: "故障信号1",
      desc: "故障信号1",
    },
    led: {
      name: "LED亮度",
      desc: "LED亮度",
    },
    waveReplay: {
      name: "录波文件",
      desc: "录波文件",
    },
  },
  // 导入导出相关
  importExport: {
    export: {
      success: "导出成功",
      failed: "导出失败",
      noData: "没有数据可导出",
      invalidPath: "文件路径无效",
    },
    import: {
      success: "导入成功",
      failed: "导入失败",
      parsing: "正在解析Excel文件...",
      validating: "正在验证数据...",
      updating: "正在更新参数...",
    },
    template: {
      export: "导出模板",
      name: "导入模板",
    },
  },
  // Excel列标题
  columns: {
    index: "序号",
    name: "名称",
    description: "描述",
    dataValue: "值",
    ang: "相角",
  },
  // 校验错误消息
  validation: {
    required: {
      cmdType: "命令类型不能为空",
      filePath: "文件路径不能为空",
      name: "第{row}行：名称字段不能为空",
    },
    format: {
      fileExtension: "只支持.xlsx格式的Excel文件",
      dataValue: "第{row}行：数据值必须是有效的数字",
      ang: "第{row}行：相角必须是有效的数字",
    },
    range: {
      angValue: "第{row}行：相角值应在-360到360度之间",
    },
    file: {
      notExists: "文件不存在：{filePath}",
      noValidData: "Excel文件中没有有效数据",
      noValidRows: "没有有效的数据可导入",
      parseError: "解析Excel文件失败：{error}",
      exportError: "导出Excel文件失败：{error}",
    },
    data: {
      validationFailed: "数据验证失败：\n{errors}",
      updateFailed: "导入数据解析成功，但更新失败",
    },
  },
  // 操作消息
  messages: {
    exportStart: "开始导出虚拟化装置参数",
    exportSuccess: "成功导出虚拟化装置参数：{filePath}，数据量：{count}",
    importStart: "开始导入虚拟化装置参数",
    importSuccess:
      "成功导入并更新虚拟化装置参数：{filePath}，更新数量：{count}",
    parseSuccess: "解析Excel文件成功，数据量：{count}",
    validDataCount: "有效数据量：{count}",
    templateExportSuccess: "成功导出导入模板：{filePath}",
  },
};