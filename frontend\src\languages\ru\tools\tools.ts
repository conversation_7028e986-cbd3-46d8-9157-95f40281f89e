export default {
  search: {
    placeholder: "Поиск по ключевым словам"
  },
  categories: {
    title: "📦IT-инструменты",
    formatting: "📝Инструменты форматирования",
    xml: "🟡Форматирование XML",
    json: "🟡Форматирование JSON",
    conversion: "🔄Инструменты конвертации",
    radix: "🟢Конвертация систем счисления",
    temperature: "🟢Конвертация температуры",
    encryption: "🔑Инструменты шифрования/дешифрования",
    textEncryption: "🔵Шифрование/дешифрование текста"
  },
  encryption: {
    title: "Шифрование/дешифрование текста",
    description:
      "Шифрование и дешифрование открытого текста с использованием алгоритмов шифрования (таких как AES, TripleDES, Rabbit или RC4)",
    encrypt: "Шифровать",
    inputText: "Текст для шифрования:",
    inputPlaceholder: "Пожалуйста, введите текст для шифрования...",
    key: "Ключ:",
    keyPlaceholder: "Пожалуйста, введите ключ шифрования",
    algorithm: "Алгоритм шифрования:",
    outputText: "Зашифрованный текст:",
    outputPlaceholder: "Результат шифрования будет отображен здесь...",
    decrypt: "Расшифровать",
    decryptInputText: "Текст для расшифровки:",
    decryptInputPlaceholder: "Пожалуйста, введите зашифрованный текст...",
    decryptKey: "Ключ:",
    decryptAlgorithm: "Алгоритм расшифровки:",
    decryptOutputText: "Расшифрованный текст:",
    decryptError: "Не удается расшифровать текст"
  },
  json: {
    title: "Форматирование JSON",
    description: "Форматирование строки JSON в удобочитаемый формат",
    sortKeys: "Сортировка полей",
    indentSize: "Размер отступа",
    inputLabel: "JSON для форматирования",
    inputPlaceholder: "Пожалуйста, вставьте ваш JSON...",
    outputLabel: "Отформатированный JSON",
    invalid: "Документ не соответствует спецификации JSON, пожалуйста, проверьте"
  },
  xml: {
    title: "Форматирование XML",
    description: "Форматирование строки XML в удобочитаемый формат",
    collapseContent: "Свернуть содержимое:",
    indentSize: "Размер отступа:",
    inputLabel: "XML для форматирования",
    inputPlaceholder: "Пожалуйста, вставьте ваш XML...",
    outputLabel: "Отформатированный XML",
    invalid: "Документ не соответствует спецификации XML, пожалуйста, проверьте"
  },
  temperature: {
    title: "Конвертация температуры",
    description:
      "Конвертация температуры между Кельвином, Цельсием, Фаренгейтом, Ранкином, Делилем, Ньютоном, Реомюром и Рёмером",
    kelvin: "Кельвин",
    kelvinUnit: "K",
    celsius: "Цельсий",
    celsiusUnit: "°C",
    fahrenheit: "Фаренгейт",
    fahrenheitUnit: "°F",
    rankine: "Ранкин",
    rankineUnit: "°R",
    delisle: "Делиль",
    delisleUnit: "°De",
    newton: "Ньютон",
    newtonUnit: "°N",
    reaumur: "Реомюр",
    reaumurUnit: "°Ré",
    romer: "Рёмер",
    romerUnit: "°Rø"
  },
  radix: {
    title: "Конвертация систем счисления",
    description:
      "Конвертация чисел между различными системами счисления (десятичная, шестнадцатеричная, двоичная, восьмеричная, base64...)",
    inputLabel: "Число для конвертации",
    inputPlaceholder: "Пожалуйста, введите число (например: 100)",
    outputLabel: "Результат конвертации",
    binary: "2-ичная(2)",
    binaryPlaceholder: "Двоичный результат...",
    octal: "8-ичная(8)",
    octalPlaceholder: "Восьмеричный результат...",
    decimal: "10-ичная(10)",
    decimalPlaceholder: "Десятичный результат...",
    hex: "16-ичная(16)",
    hexPlaceholder: "Шестнадцатеричный результат...",
    base64: "Base64(64)",
    base64Placeholder: "Результат Base64...",
    customBase: "Пользовательская система счисления",
    customBasePlaceholder: "Результат Base {{base}}..."
  },
  jsonViewer: {
    title: "Форматирование JSON",
    description: "Форматирование строки JSON в удобочитаемый формат",
    sortKeys: "Сортировка полей",
    indentSize: "Размер отступа",
    inputJson: "JSON для форматирования",
    formattedJson: "Отформатированный JSON",
    placeholder: "Пожалуйста, вставьте ваш JSON...",
    validationError: "Документ не соответствует спецификации JSON. Пожалуйста, проверьте"
  }
};
