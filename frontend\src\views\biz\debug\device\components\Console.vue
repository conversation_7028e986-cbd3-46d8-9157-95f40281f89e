<template>
  <div class="console-area">
    <div class="console-area-workbench" :style="consoleMainStyle.consoleWorkbench">
      <div>
        <span>{{ t("device.console.title") }}</span>
      </div>
      <div>
        <el-tooltip :content="t('device.console.clear')" placement="bottom">
          <i class="toolBar-icon" @click="clear">
            <svg-icon icon="ant-design:delete-outlined"></svg-icon>
          </i>
        </el-tooltip>
        <el-tooltip :content="contentDesc" placement="bottom">
          <i class="toolBar-icon" @click="hideConsole">
            <svg-icon icon="eva:minus-outline"></svg-icon>
          </i>
        </el-tooltip>
      </div>
    </div>
    <div class="console-area-input" :style="consoleMainStyle.consoleArea">
      <el-input
        ref="consoleInput"
        v-model="formatConsole"
        v-contextmenu:contextmenu
        type="textarea"
        resize="none"
        :readonly="true"
      ></el-input>
    </div>

    <!-- 拖拽调整高度的手柄 -->
    <div v-if="globalStore.isConsole" class="console-resize-handle" @mousedown="startResize" @touchstart="startResize"></div>

    <v-contextmenu ref="contextmenu">
      <v-contextmenu-item @click="selectAll">
        <svg-icon icon="ant-design:select-outlined" />
        {{ t("device.console.selectAll") }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="copy">
        <svg-icon icon="ant-design:copy-outlined" />
        {{ t("device.console.copy") }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="clear">
        <svg-icon icon="ant-design:delete-outlined" />
        {{ t("device.console.clear") }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>
<script setup lang="ts">
import Message from "@/scripts/message";
import { createScopeDebugStore, useDebugStore } from "@/stores/modules/debug";
import { isEmpty, join } from "lodash";
import { useGlobalStore } from "@/stores/modules";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { ElInput } from "element-plus";
import { useI18n } from "vue-i18n";
import { watch } from "vue";
const props = defineProps({ scopeid: String });
let debugStore;
if (props.scopeid) {
  debugStore = createScopeDebugStore(props.scopeid)();
} else {
  debugStore = useDebugStore();
}
const { consoleLog, clearConsole } = debugStore;
const globalStore = useGlobalStore();
const contextmenu = ref<ContextmenuInstance>();
const consoleInput = ref<InstanceType<typeof ElInput>>();
const { t } = useI18n();
// 移除了主题设置代码，现在使用计算属性直接设置样式
const formatConsole = computed(() => {
  if (isEmpty(consoleLog)) {
    return "";
  }
  return join(consoleLog, "\n");
});

const consoleMainStyle = computed<any>(() => {
  // 通过访问 globalStore.isDark 来确保主题变化时重新计算
  // 这样可以触发响应式更新（虽然不直接使用这个值）
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isDarkMode = globalStore.isDark;

  // 直接使用CSS变量，让浏览器处理主题切换
  // 使用与左侧菜单选中按钮一致的背景色
  const workbenchBgColor = "var(--el-menu-active-bg-color)";
  const workbenchTextColor = "var(--el-text-color-primary)";
  const borderColor = "var(--el-border-color)";
  const inputBgColor = "var(--el-bg-color)";
  const inputTextColor = "var(--el-text-color-primary)";

  if (globalStore.isConsole) {
    return {
      consoleArea: {
        height: `${globalStore.consoleHeight}px`,
        backgroundColor: inputBgColor,
        color: inputTextColor
      },
      consoleWorkbench: {
        color: workbenchTextColor,
        borderBottomColor: borderColor
      }
    };
  }
  return {
    consoleArea: {
      height: "0px",
      padding: "",
      backgroundColor: inputBgColor,
      color: inputTextColor
    },
    consoleWorkbench: {
      borderBottom: "0px",
      backgroundColor: workbenchBgColor,
      color: workbenchTextColor
    }
  };
});

// 拖拽调整高度相关变量
let isResizing = false;
let startY = 0;
let startHeight = 0;

// 开始拖拽调整高度
const startResize = (event: MouseEvent | TouchEvent) => {
  event.preventDefault();
  isResizing = true;

  if (event instanceof MouseEvent) {
    startY = event.clientY;
  } else if (event instanceof TouchEvent) {
    startY = event.touches[0].clientY;
  }

  startHeight = globalStore.consoleHeight;

  document.addEventListener("mousemove", doResize);
  document.addEventListener("mouseup", stopResize);
  document.addEventListener("touchmove", doResize);
  document.addEventListener("touchend", stopResize);

  // 添加拖拽时的样式
  document.body.style.cursor = "ns-resize";
  document.body.style.userSelect = "none";
};

// 执行拖拽调整
const doResize = (event: MouseEvent | TouchEvent) => {
  if (!isResizing) return;

  let currentY = 0;
  if (event instanceof MouseEvent) {
    currentY = event.clientY;
  } else if (event instanceof TouchEvent) {
    currentY = event.touches[0].clientY;
  }

  const deltaY = startY - currentY;
  const newHeight = Math.max(100, Math.min(500, startHeight + deltaY)); // 限制最小100px，最大500px

  globalStore.consoleHeight = newHeight;
};

// 停止拖拽调整
const stopResize = () => {
  isResizing = false;

  document.removeEventListener("mousemove", doResize);
  document.removeEventListener("mouseup", stopResize);
  document.removeEventListener("touchmove", doResize);
  document.removeEventListener("touchend", stopResize);

  // 恢复样式
  document.body.style.cursor = "";
  document.body.style.userSelect = "";
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener("mousemove", doResize);
  document.removeEventListener("mouseup", stopResize);
  document.removeEventListener("touchmove", doResize);
  document.removeEventListener("touchend", stopResize);
});

const selectAll = () => {
  consoleInput.value?.textarea?.select();
};

const copy = async () => {
  try {
    const textarea = consoleInput.value?.textarea;
    if (textarea) {
      const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
      if (selectedText) {
        await navigator.clipboard.writeText(selectedText);
        Message.success(t("device.console.copySuccess"));
      } else {
        Message.warning(t("device.console.noTextSelected"));
      }
    }
  } catch (err) {
    Message.error(t("device.console.copyFailed"));
  }
};
const clear = () => {
  clearConsole();
  Message.success(t("device.console.clearSuccess"));
};
const hideConsole = () => {
  globalStore.isConsole = !globalStore.isConsole;
};

const contentDesc = computed(() => {
  if (globalStore.isConsole) {
    return t("device.console.collapse");
  }
  return t("device.console.expand");
});

watch(
  () => consoleLog,
  () => {
    nextTick(() => {
      const textarea = consoleInput.value?.textarea;
      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    });
  },
  {
    deep: true
  }
);
</script>
<style scoped lang="scss">
.console-area {
  position: relative;
  width: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;

  .console-area-workbench {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;
    padding: 0 12px;
    background-color: var(--el-menu-active-bg-color);
    border-bottom: 1px solid var(--el-border-color);
    border-radius: 4px 4px 0 0;

    div:first-child {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 500;

      span {
        line-height: 1;
      }
    }

    div:last-child {
      display: flex;
      align-items: center;
      gap: 4px;

      .toolBar-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background-color: var(--console-hover-bg-color, var(--el-fill-color));
        }

        .svg-icon {
          font-size: 16px !important;
          color: var(--el-text-color-regular) !important;
        }
      }
    }
  }

  .console-area-input {
    overflow: hidden;

    :deep(.el-textarea) {
      .el-textarea__inner {
        height: v-bind('globalStore.consoleHeight + "px"');
        padding: 8px 12px;
        font-family: "Consolas", "Monaco", "Courier New", monospace;
        font-size: 12px;
        line-height: 1.4;
        border: none;
        border-radius: 0 0 4px 4px;
        box-shadow: none;
        resize: none;

        &:focus {
          box-shadow: none;
        }
      }
    }
  }

  .console-resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 10;
    height: 4px;
    cursor: ns-resize;
    background: transparent;

    &:hover {
      background: var(--el-color-primary-light-8);
    }

    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 2px;
      content: "";
      background: var(--el-border-color-darker);
      border-radius: 1px;
      transform: translate(-50%, -50%);
    }
  }
}
// 右键菜单样式优化
:deep(.v-contextmenu) {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);

  .v-contextmenu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    font-size: 13px;
    color: var(--el-text-color-primary);
    transition: all 0.2s;

    .svg-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      font-size: 14px;
      color: var(--el-text-color-regular);
      background: var(--el-fill-color-light);
      border-radius: 4px;
      transition: all 0.2s;
    }

    &:hover {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);

      .svg-icon {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-8);
      }
    }
  }
}
</style>
