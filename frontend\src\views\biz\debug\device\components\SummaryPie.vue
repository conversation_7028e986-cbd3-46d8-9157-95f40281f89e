<template>
  <div class="echarts">
    <ECharts ref="chartRef" :option="options" />
  </div>
</template>

<script setup lang="ts" name="pie">
import { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import { deviceSummaryApi } from "@/api/modules/biz/debug/devicesummary";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { watch } from "vue";

const options = ref({});
const { t } = useI18n();
const globalStore = useGlobalStore();
let lastResult: any = null;
const chartRef = ref();
// 简单缓存，避免重复查询同一装置时的等待
const resultCache = new Map<string, any>();

const props = defineProps<{ deviceId: string }>();

const getTextColor = () => {
  if (globalStore && typeof globalStore.isDark !== "undefined") {
    return globalStore.isDark ? "#fff" : "#222";
  }
  return window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? "#fff" : "#222";
};

type PieDatum = { name: string; value: number };

const setPieOptions = (result: any) => {
  const textColor = getTextColor();

  console.log("setPieOptions - 开始处理饶图数据:", result);

  if (result.code == 0) {
    let pieData: PieDatum[];
    const resultData: PieDatum[] = Array.isArray(result.data) ? (result.data as PieDatum[]) : [];

    console.log("setPieOptions - 解析后的数据:", {
      resultDataLength: resultData.length,
      resultData: resultData
    });

    if (resultData.length > 8) {
      const sortdata = resultData.sort((a: PieDatum, b: PieDatum) => b.value - a.value);
      const maindata = sortdata.slice(0, 10);
      const otherdata = sortdata.slice(10);
      const othersum = otherdata.reduce((sum: number, d: PieDatum) => sum + d.value, 0);
      pieData = [...maindata, { name: t("device.summaryPie.other"), value: othersum }];
    } else {
      pieData = resultData;
    }

    console.log("setPieOptions - 最终饶图数据:", pieData);

    const option: ECOption = {
      title: {
        text: t("device.summaryPie.title"),
        subtext: t("device.summaryPie.subtext"),
        left: "65%",
        top: "46%",
        textAlign: "center",
        textStyle: {
          fontSize: 16,
          color: textColor
        },
        subtextStyle: {
          fontSize: 15,
          color: textColor
        }
      },
      tooltip: {
        trigger: "item",
        confine: true,
        formatter: (params: any) => `${params.name}: ${params.value} (${params.percent}%)`
      },
      legend: {
        top: 10,
        left: 10,
        type: "scroll",
        orient: "vertical",
        pageButtonItemGap: 6,
        pageButtonGap: 8,
        pageFormatter: "{current}/{total}",
        icon: "circle", //图例形状
        align: "left",
        itemGap: 15,
        textStyle: {
          fontSize: 12,
          fontWeight: 500,
          color: textColor
        },
        formatter: function (name: string) {
          let dataCopy = "";
          for (let i = 0; i < pieData.length; i++) {
            if (pieData[i].name == name && pieData[i].value >= 10000) {
              dataCopy = (pieData[i].value / 10000).toFixed(2);
              return name + "      " + dataCopy + "w";
            } else if (pieData[i].name == name) {
              dataCopy = pieData[i].value + "";
              return name + "      " + dataCopy;
            }
          }
          return "";
        }
      },
      series: [
        {
          type: "pie",
          radius: ["35%", "64%"], // 增大中间环，减小内径
          center: ["67%", "50%"], // 往右移动饼图
          avoidLabelOverlap: true,
          minShowLabelAngle: 10,
          selectedMode: false,
          silent: false, // 支持交互
          clockwise: true,
          startAngle: 150,

          data: pieData,
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          color: [
            "#5470C6",
            "#91CC75",
            "#FAC858",
            "#EE6666",
            "#73C0DE",
            "#3BA272",
            "#FC8452",
            "#9A60B4",
            "#EA7CCC",
            "#6E7074",
            "#546570",
            "#C4CCD3"
          ]
        }
      ]
    };
    options.value = option;
  } else {
    console.log("setPieOptions - API返回码不为0或数据为空:", {
      code: result?.code,
      data: result?.data
    });
    // 没有数据时，设置空的饶图
    options.value = {
      title: {
        text: t("device.summaryPie.title"),
        subtext: t("device.summaryPie.subtext"),
        left: "65%",
        top: "46%",
        textAlign: "center",
        textStyle: {
          fontSize: 16,
          color: getTextColor()
        },
        subtextStyle: {
          fontSize: 15,
          color: getTextColor()
        }
      },
      series: [
        {
          type: "pie",
          radius: ["35%", "64%"],
          center: ["67%", "50%"],
          data: [],
          label: { show: false },
          labelLine: { show: false }
        }
      ]
    };
  }
};

onMounted(async () => {
  // 优先从缓存读取，减少重复请求
  let result = resultCache.get(props.deviceId);
  if (!result) {
    result = await deviceSummaryApi.getParamSummaryByDevice(props.deviceId);
    resultCache.set(props.deviceId, result);
  }

  // 添加调试信息
  console.log("SummaryPie - API返回结果:", result);
  console.log("SummaryPie - 数据类型检查:", {
    code: result?.code,
    dataIsArray: Array.isArray(result?.data),
    dataLength: result?.data?.length,
    data: result?.data
  });

  lastResult = result;
  setPieOptions(result);

  // 监听浏览器主题变化
  if (window.matchMedia) {
    window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", () => {
      setPieOptions(lastResult);
    });
  }
});

// 监听菜单栏主题切换
watch(
  () => globalStore.isDark,
  () => {
    setPieOptions(lastResult);
  }
);

// const pieData = [
//   { value: 5000, name: "Gitee 访问量" },
//   { value: 5000, name: "GitHub 访问量" }
// ];
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
