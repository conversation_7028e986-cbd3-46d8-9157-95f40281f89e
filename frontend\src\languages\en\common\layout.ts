export default {
  language: {
    title: "Language",
    zh: "Simplified Chinese",
    en: "English",
    es: "Spanish",
    fr: "French",
    ru: "Russian",
    tooltip: "Multilingual"
  },
  about: {
    title: "About",
    introduction: "Introduction",
    description:
      "A new generation of high-performance platform debugging tool developed with the latest technology stack such as Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc.",
    versionInfo: "Version Info",
    toolName: "Tool Name",
    version: "Version",
    machineCode: "Machine Code",
    loading: "Loading...",
    machineCodeError: "Failed to get",
    copySuccess: "Machine code copied to clipboard",
    copyError: "Copy failed",
    versionFeatures: "Version Features",
    features: {
      visualTool:
        "Includes visual tool connection, device information viewing, settings, analog quantity, status quantity, remote signaling, remote measurement, remote control, report, device time synchronization, setting import/export, variable debugging functions",
      configTool: "Includes configuration tool preview, add, edit, custom symbol, associate device information functions",
      themeTool: "Includes theme customization, IT gadgets, device configuration import/export functions"
    }
  },
  footer: {
    copyright: "{version}"
  },
  header: {
    minimize: "Minimize",
    maximize: "Maximize",
    restore: "Restore",
    close: "Close",
    company: {
      name: "Sieyuan Electric",
      englishName: "Sieyuan"
    },
    collapse: {
      expand: "Expand Device",
      fold: "Collapse Device",
      expandTool: "Expand Tool List",
      foldTool: "Collapse Tool List"
    },
    breadcrumb: {
      home: "Home"
    },
    assemblySize: {
      title: "Size Setting",
      default: "Default",
      large: "Large",
      small: "Small"
    },
    avatar: {
      profile: "Profile",
      switchApp: "Switch App",
      logout: "Logout",
      logoutConfirm: {
        title: "Reminder",
        message: "Are you sure you want to logout?",
        confirm: "Confirm",
        cancel: "Cancel"
      },
      logoutSuccess: "Logout successful!"
    },
    changeModule: {
      title: "Switch Module"
    },
    enginConfig: {
      configType: "Config Type",
      openDirectory: "Open File Directory",
      cancel: "Cancel",
      confirm: "Confirm",
      all: "All",
      deviceList: "Device List",
      configureList: "Configuration List",
      exportSuccess: "Export configuration successful",
      importSuccess: "Import configuration successful",
      disconnectDeviceFirst: "Please disconnect the connected device first",
      overrideConfirm: "Configuration list already exists, overwrite?",
      warmTips: "Reminder",
      importConfigFile: "Import configuration file",
      zipFiles: "Zip Files",
      allFiles: "All Files"
    },
    userInfo: {
      title: "User Info",
      cancel: "Cancel",
      confirm: "Confirm"
    },
    password: {
      title: "Change Password",
      cancel: "Cancel",
      confirm: "Confirm"
    },
    globalSetting: {
      title: "Settings",
      tooltip: "Settings"
    },
    moreInfo: {
      title: "More",
      tooltip: "More",
      items: {
        importConfig: "Import Config",
        printScreen: "Screenshot",
        search: "Search Menu",
        exportConfig: "Export Config",
        remoteControl: "Remote Control",
        about: "About",
        help: "Help"
      },
      importConfig: {
        title: "Import Project Config",
        placeholder: "Please select the configuration file path to import"
      },
      exportConfig: {
        title: "Export Project Config",
        placeholder: "Please select the export directory"
      }
    },
    remoteControl: {
      title: "Remote Control",
      connectionConfig: "Connection Configuration",
      host: "Host Address",
      hostPlaceholder: "Enter IP address or hostname",
      hostRequired: "Please enter host address",
      hostInvalid: "Please enter a valid IP address or hostname",
      port: "Port",
      portRequired: "Please enter port number",
      username: "Username",
      usernamePlaceholder: "Enter username (optional)",
      password: "Password",
      passwordPlaceholder: "Enter password (optional)",
      connect: "Connect",
      disconnect: "Disconnect",
      reset: "Reset",
      connecting: "Connecting...",
      connected: "Connected",
      disconnected: "Disconnected",
      connectSuccess: "Connection successful",
      connectFailed: "Connection failed",
      connectError: "Connection error, please check network and configuration",
      disconnectConfirm: "Are you sure you want to disconnect the remote connection?",
      disconnectSuccess: "Disconnection successful",
      warning: "Warning",
      confirm: "Confirm",
      cancel: "Cancel",
      fullscreen: "Fullscreen",
      screenshot: "Screenshot",
      screenshotTaken: "Screenshot saved",
      ctrlAltDelSent: "Ctrl+Alt+Del sent",
      credentialsRequired: "Authentication required",
      libraryLoadError: "Failed to load VNC library"
    },
    searchMenu: {
      placeholder: "Menu search: supports menu name, path",
      empty: "No menu"
    },
    theme: {
      title: "Theme",
      tooltip: "Theme"
    }
  },
  main: {
    maximize: {
      exit: "Exit Maximize"
    }
  },
  theme: {
    title: "Layout Settings",
    quickTheme: {
      title: "Theme Settings"
    },
    layoutSettings: {
      title: "Layout Settings"
    },
    layout: {
      title: "Layout Style",
      columns: "Columns",
      classic: "Classic",
      transverse: "Transverse",
      vertical: "Vertical"
    },
    global: {
      title: "Global Theme",
      primary: "Theme Color",
      dark: "Dark Mode",
      grey: "Grey Mode",
      weak: "Weak Mode",
      special: "Special Mode"
    },
    mode: {
      light: "Light",
      dark: "Dark"
    },
    interface: {
      title: "Interface Settings",
      watermark: "Watermark",
      breadcrumb: "Breadcrumb",
      breadcrumbIcon: "Breadcrumb Icon",
      tabs: "Tabs Bar",
      tabsIcon: "Tabs Bar Icon",
      footer: "Footer",
      drawerForm: "Drawer Form"
    },
    presetThemes: {
      title: "Preset Themes",
      default: {
        name: "Default Theme",
        description: "Classic blue theme"
      },
      dark: {
        name: "Dark Theme",
        description: "Eye protection dark mode"
      },
      techBlue: {
        name: "Tech Blue",
        description: "Modern tech blue"
      },
      deepBlue: {
        name: "Deep Blue",
        description: "Deep ocean blue"
      },
      nature: {
        name: "Nature Theme",
        description: "Fresh green"
      },
      forestGreen: {
        name: "Forest Green",
        description: "Deep forest green"
      },
      warm: {
        name: "Warm Theme",
        description: "Warm orange"
      },
      sunsetOrange: {
        name: "Sunset Orange",
        description: "Warm sunset orange"
      },
      elegant: {
        name: "Elegant Theme",
        description: "Noble purple"
      },
      lavender: {
        name: "Lavender",
        description: "Soft lavender purple"
      },
      sakura: {
        name: "Sakura Pink",
        description: "Romantic sakura pink"
      },
      rose: {
        name: "Rose Red",
        description: "Passionate rose red"
      },
      lime: {
        name: "Lime Green",
        description: "Vibrant lime green"
      },
      skyBlue: {
        name: "Sky Blue",
        description: "Clear sky blue"
      },
      eyeCare: {
        name: "Eye Care Mode",
        description: "Grey eye care theme"
      }
    },
    colors: {
      techBlue: {
        name: "Tech Blue",
        description: "Modern tech feel"
      },
      natureGreen: {
        name: "Nature Green",
        description: "Fresh and natural"
      },
      vibrantOrange: {
        name: "Vibrant Orange",
        description: "Warm and vibrant"
      },
      elegantPurple: {
        name: "Elegant Purple",
        description: "Noble and elegant"
      },
      romanticPink: {
        name: "Romantic Pink",
        description: "Gentle and romantic"
      },
      freshCyan: {
        name: "Fresh Cyan",
        description: "Fresh and elegant"
      },
      brightYellow: {
        name: "Bright Yellow",
        description: "Bright and lively"
      },
      warmOrange: {
        name: "Warm Orange",
        description: "Warm and comfortable"
      },
      limeGreen: {
        name: "Lime Green",
        description: "Fresh lime"
      },
      deepBlue: {
        name: "Deep Blue",
        description: "Deep and steady"
      },
      golden: {
        name: "Golden",
        description: "Classic gold"
      },
      chinaRed: {
        name: "China Red",
        description: "Traditional red"
      }
    }
  },
  tabs: {
    moreButton: {
      refresh: "Refresh",
      closeCurrent: "Close Current",
      closeLeft: "Close Left",
      closeRight: "Close Right",
      closeOthers: "Close Others",
      closeAll: "Close All"
    }
  }
};
