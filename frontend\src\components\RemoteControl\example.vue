<template>
  <div class="remote-control-example">
    <el-card>
      <template #header>
        <h3>远程控制功能演示</h3>
      </template>
      
      <div class="demo-buttons">
        <el-button type="primary" @click="openRemoteControl">
          <el-icon><Monitor /></el-icon>
          打开远程控制
        </el-button>
        
        <el-button @click="showStats">
          <el-icon><DataAnalysis /></el-icon>
          查看统计信息
        </el-button>
        
        <el-button @click="testConnection">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
      </div>
      
      <div class="demo-info" v-if="stats">
        <h4>会话统计</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总会话数">{{ stats.total }}</el-descriptions-item>
          <el-descriptions-item label="已连接">{{ stats.connected }}</el-descriptions-item>
          <el-descriptions-item label="连接中">{{ stats.connecting }}</el-descriptions-item>
          <el-descriptions-item label="错误">{{ stats.error }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="demo-presets">
        <h4>预设配置示例</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <h5>本地测试</h5>
              <p>主机: 127.0.0.1</p>
              <p>端口: 5900</p>
              <el-button size="small" @click="connectPreset('local')">连接</el-button>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <h5>开发服务器</h5>
              <p>主机: *************</p>
              <p>端口: 5900</p>
              <el-button size="small" @click="connectPreset('dev')">连接</el-button>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <h5>生产服务器</h5>
              <p>主机: **********</p>
              <p>端口: 5900</p>
              <el-button size="small" @click="connectPreset('prod')">连接</el-button>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 远程控制对话框 -->
    <RemoteControlDialog ref="remoteControlRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Monitor, DataAnalysis, Connection } from "@element-plus/icons-vue";
import RemoteControlDialog from "@/layouts/components/Header/components/RemoteControlDialog.vue";
import { remoteControlApi } from "@/api/modules/sys/remote";

const remoteControlRef = ref();
const stats = ref(null);

// 打开远程控制对话框
const openRemoteControl = () => {
  remoteControlRef.value?.openDialog();
};

// 查看统计信息
const showStats = async () => {
  try {
    const result = await remoteControlApi.getSessions();
    if (result.data) {
      // 计算统计信息
      const sessions = result.data;
      stats.value = {
        total: sessions.length,
        connected: sessions.filter(s => s.status === 'connected').length,
        connecting: sessions.filter(s => s.status === 'connecting').length,
        error: sessions.filter(s => s.status === 'error').length
      };
      ElMessage.success("统计信息已更新");
    }
  } catch (error) {
    console.error("获取统计信息失败:", error);
    ElMessage.error("获取统计信息失败");
  }
};

// 测试连接
const testConnection = async () => {
  try {
    const result = await remoteControlApi.testConnection({
      host: "127.0.0.1",
      port: 5900
    });
    
    if (result.data) {
      ElMessage.success("连接测试成功");
    } else {
      ElMessage.error("连接测试失败");
    }
  } catch (error) {
    console.error("连接测试失败:", error);
    ElMessage.error("连接测试失败");
  }
};

// 连接预设配置
const connectPreset = (presetName: string) => {
  const presets = {
    local: { host: "127.0.0.1", port: 5900 },
    dev: { host: "*************", port: 5900 },
    prod: { host: "**********", port: 5900 }
  };
  
  const preset = presets[presetName];
  if (preset) {
    ElMessage.info(`正在连接到 ${preset.host}:${preset.port}`);
    // 这里可以直接调用连接API或者打开对话框并预填配置
    openRemoteControl();
  }
};
</script>

<style scoped>
.remote-control-example {
  padding: 20px;
}

.demo-buttons {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.demo-info {
  margin: 20px 0;
}

.demo-presets {
  margin-top: 20px;
}

.demo-presets h4 {
  margin-bottom: 16px;
}

.demo-presets .el-card {
  text-align: center;
}

.demo-presets h5 {
  margin: 0 0 12px 0;
  color: var(--el-color-primary);
}

.demo-presets p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}
</style>
