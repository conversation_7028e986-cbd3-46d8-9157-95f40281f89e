<template>
  <div class="scroll-container">
    <el-scrollbar v-if="searchForData.length > 0" class="scrollbar-list" :height="getTreeScrollbar()">
      <div
        v-for="item in searchForData"
        class="device-item flx-center card"
        :key="item.key"
        :class="fnClass(item)"
        @click="handleListClick(item)"
      >
        <!-- 样式 -->
        <div class="pinned-tag"></div>
        <!-- 连接状态 -->
        <el-badge :hidden="false">
          <svg-icon :icon="item.icon" class="status-icon"></svg-icon>
        </el-badge>
        <!-- 装置 -->
        <div class="device-item-right">
          <div class="device-item-right-left">
            <div class="device-name flx-start">
              <span class="name-title">{{ item.name }}</span>
            </div>
            <div class="device-item-right-bottom">
              {{ item?.desc || t("matrix.functionList.unnamedDevice") }}
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { FucntionItem } from "@/api/interface/biz/matrix";
import { useGlobalStore } from "@/stores/modules";
import { forEach, set } from "lodash";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();
const globalStore = useGlobalStore();

// 1. 声明组件事件
const emit = defineEmits(["change"]);

const searchForData = ref<FucntionItem[]>([]);

const getTreeScrollbar = () => {
  let offset = 0;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return `calc(100vh - 90px - ${offset}px)`;
};

const fnClass = item => {
  if (item.isActive == true) {
    return "is-active";
  }
  return "";
};

// 点击装置
const handleListClick = data => {
  console.log("点击 handleListClick:", data);
  setCurrDevice(data);
  emit("change", data);
};

const setCurrDevice = data => {
  forEach(searchForData.value, item => {
    set(item, "isActive", false);
  });
  data.isActive = true;
};
const initData = async () => {
  const menuItems = [
    {
      key: "batch",
      name: t("matrix.functionList.batchDownload.name"),
      desc: t("matrix.functionList.batchDownload.desc"),
      icon: "ant-design:codepen-square-filled",
      component: "MatrixContent"
    },
    {
      key: "package",
      name: t("matrix.functionList.packageProgram.name"),
      desc: t("matrix.functionList.packageProgram.desc"),
      icon: "ant-design:folder-open-filled",
      component: "PackageProgram"
    },
    {
      key: "xml",
      name: t("matrix.functionList.xmlFormatter.name"),
      desc: t("matrix.functionList.xmlFormatter.desc"),
      icon: "ant-design:linkedin-outlined",
      component: "XmlFormatter"
    },
    {
      key: "json",
      name: t("matrix.functionList.jsonFormatter.name"),
      desc: t("matrix.functionList.jsonFormatter.desc"),
      icon: "ant-design:java-script-outlined",
      component: "JsonViewer"
    },
    {
      key: "radix",
      name: t("matrix.functionList.radixConverter.name"),
      desc: t("matrix.functionList.radixConverter.desc"),
      icon: "ant-design:control-filled",
      component: "IntegerBaseConverter"
    },
    {
      key: "temperature",
      name: t("matrix.functionList.temperatureConverter.name"),
      desc: t("matrix.functionList.temperatureConverter.desc"),
      icon: "ant-design:bulb-filled",
      component: "TemperatureConverter"
    },
    {
      key: "crypto",
      name: t("matrix.functionList.encryption.name"),
      desc: t("matrix.functionList.encryption.desc"),
      icon: "ant-design:lock-filled",
      component: "Encryption"
    }
  ];
  searchForData.value = menuItems;
  setCurrDevice(searchForData.value[0]);
};

onMounted(async () => {
  await initData();
});

// 监听语言变化
watch(locale, async () => {
  await initData();
});
</script>

<style lang="scss" scoped>
.scroll-container {
  position: relative;
  width: 100%;
  border: 1px solid var(--el-border-color);
  .scrollbar-list {
    background: var(--el-bg-color);
  }
  .device-item {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 65px;
    padding: 12px 16px;
    margin: 3px;
    overflow: hidden;
    color: var(--el-text-color);
    cursor: pointer;
    user-select: none;
    &:hover {
      background: var(--bl-hover-bg-color);
    }
    &:last-child::after {
      display: none; // 最后一个item不需要分割线
    }
    .pinned-tag {
      position: absolute;
      top: 3px;
      left: 3px;
      display: block;
      border: 6px solid var(--el-color-primary);
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-radius: 2px;
      opacity: 0.8;
    }
    .status-icon {
      margin-right: 10px;
      font-size: 35px;
      &.connected {
        color: limegreen;
      }
      &.disconnected {
        color: red;
      }
    }
    .device-item-right {
      position: relative;
      width: 100%;
      height: 44px;
      margin-top: 6px;
      .device-item-right-left {
        width: 100%;
        .device-name {
          max-width: 180px;
          max-height: 18px;
          font-size: 14px;
          line-height: 18px;
          color: var(--el-text-color);
          .name-title {
            padding-right: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .device-item-right-bottom {
        overflow: auto;
        font-size: 11px;
        color: red;

        // white-space: nowrap;
        color: var(--el-text-color-secondary);
      }
      .device-item-right-right {
        float: right;
        width: auto;
        overflow: hidden;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        text-overflow: ellipsis;
        white-space: nowrap;
        &.connected {
          color: limegreen;
        }
        &.disconnected {
          color: red;
        }
      }
    }
  }
  .device-item-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--el-text-color-secondary);
    background: var(--el-bg-color);
  }
  .is-active {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    background: var(--el-color-primary-light-9) !important;
  }
}
</style>
