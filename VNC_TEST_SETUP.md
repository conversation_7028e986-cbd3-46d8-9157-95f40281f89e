# VNC测试环境设置

## 概述

为了测试VNC远程控制功能，您需要设置一个VNC服务器。以下是在不同操作系统上设置VNC服务器的方法。

## Windows系统

### 使用TightVNC Server

1. **下载并安装TightVNC Server**
   - 访问 https://www.tightvnc.com/download.php
   - 下载并安装TightVNC Server

2. **配置TightVNC**
   - 设置访问密码
   - 配置端口（默认5900）
   - 启动服务

3. **防火墙设置**
   - 允许端口5900通过Windows防火墙

### 使用UltraVNC

1. **下载并安装UltraVNC**
   - 访问 https://www.uvnc.com/downloads/ultravnc.html
   - 安装UltraVNC Server

2. **配置**
   - 设置密码
   - 配置端口
   - 启动服务

## Linux系统

### 使用x11vnc

1. **安装x11vnc**
```bash
# Ubuntu/Debian
sudo apt-get install x11vnc

# CentOS/RHEL
sudo yum install x11vnc
```

2. **启动VNC服务器**
```bash
# 设置密码
x11vnc -storepasswd

# 启动服务器
x11vnc -forever -usepw -display :0
```

### 使用TigerVNC

1. **安装TigerVNC**
```bash
# Ubuntu/Debian
sudo apt-get install tigervnc-standalone-server

# CentOS/RHEL
sudo yum install tigervnc-server
```

2. **配置并启动**
```bash
# 设置密码
vncpasswd

# 启动服务器
vncserver :1
```

## macOS系统

### 使用内置屏幕共享

1. **启用屏幕共享**
   - 系统偏好设置 > 共享
   - 勾选"屏幕共享"
   - 设置访问权限

2. **VNC访问**
   - 默认端口：5900
   - 使用系统用户密码

### 使用第三方VNC服务器

1. **安装RealVNC**
   - 下载并安装RealVNC Server
   - 配置访问密码和端口

## 测试连接

### 基本测试步骤

1. **确认VNC服务器运行**
   - 检查端口是否监听：`netstat -an | grep 5900`

2. **本地测试**
   - 使用VNC客户端连接到 `localhost:5900`

3. **远程测试**
   - 从另一台机器连接到服务器IP

### 使用本应用测试

1. **打开远程控制功能**
   - 点击"更多" > "远程控制"

2. **输入连接信息**
   - 主机地址：VNC服务器IP
   - 端口：5900（或自定义端口）
   - 用户名/密码：根据VNC服务器配置

3. **建立连接**
   - 点击"连接"按钮
   - 等待连接建立

## 常见问题

### 连接失败

1. **检查网络连通性**
```bash
ping [VNC服务器IP]
telnet [VNC服务器IP] 5900
```

2. **检查防火墙**
   - 确保VNC端口未被防火墙阻止

3. **检查VNC服务器状态**
   - 确认VNC服务器正在运行
   - 检查端口配置

### 身份验证失败

1. **检查密码**
   - 确认VNC服务器密码设置正确

2. **检查用户权限**
   - 某些VNC服务器需要特定用户权限

### 性能问题

1. **网络带宽**
   - 确保网络带宽足够

2. **VNC服务器设置**
   - 调整图像质量设置
   - 启用压缩

## 安全建议

1. **使用强密码**
   - 设置复杂的VNC访问密码

2. **限制访问**
   - 配置IP访问限制
   - 使用VPN连接

3. **加密连接**
   - 考虑使用SSH隧道
   - 使用支持加密的VNC服务器

## 开发测试

### 本地测试环境

1. **在同一台机器上运行VNC服务器**
   - 连接地址：127.0.0.1:5900

2. **使用虚拟机**
   - 在虚拟机中安装VNC服务器
   - 从主机连接到虚拟机

### 调试技巧

1. **查看控制台日志**
   - 检查前端控制台错误
   - 查看后端服务日志

2. **网络抓包**
   - 使用Wireshark分析VNC协议数据

3. **逐步测试**
   - 先测试TCP连接
   - 再测试VNC握手
   - 最后测试完整功能

## 示例配置

### Windows TightVNC配置
```
端口: 5900
密码: your_password
允许连接: 任何IP（测试用）
```

### Linux x11vnc启动命令
```bash
x11vnc -forever -usepw -display :0 -rfbport 5900
```

### 应用连接配置
```
主机地址: *************
端口: 5900
用户名: （留空或根据服务器要求）
密码: your_password
```