import { defineConfig, loadEnv, ConfigEnv, UserConfig } from "vite";
import { resolve } from "path";
import { wrapperEnv } from "./build/getEnv";
import { createProxy } from "./build/proxy";
import { createVitePlugins } from "./build/plugins";
import { visualizer } from "rollup-plugin-visualizer";
import pkg from "./package.json";
import dayjs from "dayjs";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

const { dependencies, devDependencies, name } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};

// @see: https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);

  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve("node_modules/async-validator/dist-node/index.js")
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/var.scss";@import "@/styles/utils.scss";`,
          api: "modern-compiler" // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        "element-plus",
        "element-plus/es",
        "element-plus/es/components/base/style/css",
        "element-plus/es/components/button/style/css",
        "element-plus/es/components/input/style/css",
        "element-plus/es/components/tooltip/style/css",
        "element-plus/es/components/dialog/style/css",
        "element-plus/es/components/row/style/css",
        "element-plus/es/components/watermark/style/css",
        "element-plus/es/components/container/style/css",
        "element-plus/es/components/aside/style/css",
        "element-plus/es/components/scrollbar/style/css",
        "element-plus/es/components/menu/style/css",
        "element-plus/es/components/header/style/css",
        "element-plus/es/components/drawer/style/css",
        "element-plus/es/components/switch/style/css",
        "element-plus/es/components/color-picker/style/css",
        "element-plus/es/components/divider/style/css",
        "element-plus/es/components/icon/style/css",
        "element-plus/es/components/badge/style/css",
        "element-plus/es/components/tabs/style/css",
        "element-plus/es/components/tab-pane/style/css",
        "element-plus/es/components/menu-item/style/css",
        "element-plus/es/components/sub-menu/style/css",
        "element-plus/es/components/dropdown/style/css",
        "element-plus/es/components/dropdown-menu/style/css",
        "element-plus/es/components/dropdown-item/style/css",
        "element-plus/es/components/popover/style/css",
        "element-plus/es/components/progress/style/css",
        "element-plus/es/components/footer/style/css",
        "element-plus/es/components/main/style/css",
        "element-plus/es/components/form/style/css",
        "element-plus/es/components/input-number/style/css",
        "element-plus/es/components/form-item/style/css",
        "element-plus/es/components/select/style/css",
        "element-plus/es/components/option/style/css",
        "element-plus/es/components/breadcrumb/style/css",
        "element-plus/es/components/breadcrumb-item/style/css",
        "element-plus/es/components/empty/style/css",
        "element-plus/es/components/tree/style/css",
        "element-plus/es/components/descriptions/style/css",
        "element-plus/es/components/descriptions-item/style/css",
        "element-plus/es/components/tag/style/css",
        "element-plus/es/components/col/style/css",
        "element-plus/es/components/card/style/css",
        "element-plus/es/components/text/style/css",
        "element-plus/es/components/date-picker/style/css",
        "element-plus/es/components/message/style/css",
        "element-plus/es/components/message-box/style/css",
        "element-plus/es/components/checkbox/style/css",
        "element-plus/es/components/table/style/css",
        "element-plus/es/components/table-column/style/css",
        "element-plus/es/components/radio/style/css",
        "element-plus/es/components/pagination/style/css",
        "element-plus/es/components/space/style/css"
      ]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // 自动引入 Element Plus 组件
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      // 自动引入 Element Plus API
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      })
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      // esbuild 打包更快，但是不能去除 console.log，terser打包慢，但能去除 console.log
      // minify: "terser",
      // terserOptions: {
      // 	compress: {
      // 		drop_console: viteEnv.VITE_DROP_CONSOLE,
      // 		drop_debugger: true
      // 	}
      // },
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        output: {
          // Static resource classification and packaging
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]"
        }
      }
    }
  };
});
