<!-- 所有装置详情 -->
<template>
  <div class="device-tabs" :style="deviceListStyle.deviceNotEmpty">
    <KeepAlive>
      <el-tabs
        v-if="initDeviceList.length > 0 && debugStore.currDevice.isConnect"
        v-model="activeTab"
        type="border-card"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="item in initDeviceList" :key="item.id" :label="item.ip" :name="item.id">
          <template #label>
            <span class="custom-tabs-label">
              <el-icon><ChromeFilled /></el-icon>
              <span>{{ item.ip }}</span>
            </span>
          </template>
          <Device :device-model="item" :key="item.id" />
        </el-tab-pane>
      </el-tabs>
      <div v-else class="device-item-empty flx-center" :style="deviceListStyle.deviceEmpty">
        <!-- <span>请连接装置</span>
      <img src="path/to/your/image.png" alt="请连接装置" /> -->

        <img src="@/assets/images/not_connected.png" class="not-img" :alt="t('device.devices.notConnectedAlt')" />
        <div class="not-detail">
          <!-- <h2>404</h2> -->
          <h4>{{ t("device.devices.pleaseConnect") }}</h4>
        </div>
      </div>
    </KeepAlive>
  </div>
</template>

<script setup lang="ts">
import { ComputedRef, ref } from "vue";
import Device from "./Device.vue";
import { find } from "lodash";
const activeTab = ref();
const currentDevice = ref<DebugDeviceInfo>({
  id: "",
  name: "",
  ip: "",
  port: "",
  encrypted: false,
  prjType: 1,
  deviceType: 1,
  isConnect: false,
  isActive: false,
  connectTimeout: 10000,
  connectTime: ""
});

import { TabsPaneContext } from "element-plus";
import { useDebugStore } from "@/stores/modules/debug";
import { filter, forEach, set } from "lodash";
import { useGlobalStore } from "@/stores/modules";
import { DebugDeviceInfo } from "@/stores/interface";
import { useI18n } from "vue-i18n";

const debugStore = useDebugStore();
const globalStore = useGlobalStore();
const { t } = useI18n();

const deviceListStyle = computed<any>(() => {
  let offset = 86;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  if (globalStore.isConsole) {
    offset += globalStore.consoleHeight + 1; // 使用动态控制台高度，+1是为了边框
  }
  return {
    deviceEmpty: { height: `calc(100vh  - ${offset}px - 2px)` },
    deviceNotEmpty: { height: `calc(100vh  - ${offset}px)` }
  };
});
const tabClick = (tabItem: TabsPaneContext) => {
  const deviceId = tabItem.props.name as string;
  forEach(debugStore.deviceList, item => {
    set(item, "isActive", false);
  });
  const device = find(debugStore.deviceList, { id: deviceId });
  if (device) {
    device.isActive = true;
    debugStore.setCurrDevice(device);
  }
};

const initDeviceList: ComputedRef<DebugDeviceInfo[]> = computed(() => {
  const deviceListAll = debugStore.deviceList;
  deviceListAll.forEach(item => {
    if (item.isActive) {
      activeTab.value = item.id;
      currentDevice.value = item;
    }
  });
  return [
    ...filter(deviceListAll, obj => {
      return obj.isConnect;
    })
  ];
});
</script>

<style scoped lang="scss">
.device-tabs {
  width: 100%;
  height: auto;
  margin-bottom: 5px;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__content) {
    padding: 3px;
  }
}
.device-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}
.not-img {
  width: 300px;
  height: 300px;
  margin-right: 50px;
}
.device-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.device-tabs .custom-tabs-label span {
  margin-left: 4px;
  vertical-align: middle;
}
.device-item-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 100%;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
}
</style>
