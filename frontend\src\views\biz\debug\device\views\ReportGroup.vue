<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="uploadWave">{{ t("device.reportGroup.contextMenu.uploadWave") }}</v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="searchReport">{{ t("device.reportGroup.contextMenu.getHistoryReport") }}</v-contextmenu-item>
    <v-contextmenu-item @click="exportReport">{{ t("device.reportGroup.contextMenu.saveResult") }}</v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="clearList">{{ t("device.reportGroup.contextMenu.clearContent") }}</v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page" @click="hideContextMenu">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("device.reportGroup.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{
          t("device.reportGroup.search")
        }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{
          t("device.reportGroup.save")
        }}</el-button>
        <el-button type="primary" plain :icon="Refresh" @click="refreshReport" :loading="refreshLoading">{{
          refreshName
        }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("device.reportGroup.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table">
      <!-- 使用 Element Plus Tree V2 虚拟化树形控件 -->
      <div v-if="isTreeData && shouldUseVirtualization" class="virtualized-tree-container">
        <!-- 表格头部 - 固定高度和宽度，防止数据加载后变动 -->
        <div class="tree-table-header fixed-header">
          <div class="header-cell fixed-width-240">{{ t("device.reportGroup.table.reportId") }}</div>
          <div class="header-cell fixed-width-300">{{ t("device.reportGroup.table.time") }}</div>
          <div class="header-cell flex-1">{{ t("device.reportGroup.table.description") }}</div>
        </div>

        <!-- Tree V2 虚拟化树形控件 -->
        <el-tree-v2
          v-loading="tableLoad"
          :data="treeTableData"
          :props="treeProps"
          :height="treeHeight"
          :item-size="32"
          :expand-on-click-node="false"
          :default-expand-all="true"
          :default-expanded-keys="defaultExpandedKeys"
          node-key="id"
          @node-click="handleTreeNodeClick"
        >
          <template #default="{ data }">
            <div
              class="tree-table-row"
              @contextmenu="e => handleTreeRowContextMenu(e, data)"
              @click="() => handleTreeNodeClick(data)"
            >
              <div class="tree-cell fixed-width-240">
                <span :title="data.name">{{ data.name }}</span>
              </div>
              <div class="tree-cell fixed-width-300">
                <span :title="data.value">{{ data.value }}</span>
              </div>
              <div class="tree-cell flex-1">
                <span :title="data.ret_ms">{{ data.ret_ms }}</span>
              </div>
            </div>
          </template>
        </el-tree-v2>
      </div>

      <!-- 普通表格（数据量小或平面数据时使用） -->
      <el-table
        v-else
        v-loading="tableLoad"
        v-contextmenu:contextmenu="contextmenu"
        border
        :row-key="isTreeData ? 'id' : 'faultNo'"
        :data="tableData"
        :max-height="getTableMaxHeight(220)"
        highlight-current-row
        :default-expand-all="isTreeData"
        :tree-props="isTreeData ? { children: 'children', hasChildren: 'hasChildren' } : undefined"
        :show-header="true"
        :stripe="true"
        :element-loading-text="t('device.reportGroup.loading')"
        table-layout="fixed"
        :header-row-style="{ height: tableFixedConfig.headerHeight + 'px' }"
        @cell-contextmenu="cellContextmenu"
        @cell-click="cellContextmenu"
        @click="hideContextMenu"
      >
        <!-- 根据数据类型显示不同的列 -->
        <template v-if="isTreeData">
          <!-- 树形数据显示 - 固定列宽，防止数据加载后变动 -->
          <el-table-column
            :label="t('device.reportGroup.table.reportId')"
            width="240"
            prop="name"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            :label="t('device.reportGroup.table.time')"
            width="300"
            prop="value"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            :label="t('device.reportGroup.table.description')"
            prop="ret_ms"
            align="center"
            :resizable="false"
          ></el-table-column>
        </template>
        <template v-else>
          <!-- 平面数据显示 - 固定列宽，防止数据加载后变动 -->
          <el-table-column
            :label="t('device.reportGroup.table.reportId')"
            width="240"
            prop="faultNo"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            :label="t('device.reportGroup.table.time')"
            width="300"
            prop="faultStartTime"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column :label="t('device.reportGroup.table.description')" prop="st" align="center" :resizable="false">
            <template #default="scope">
              <span>{{ scope.row.st?.length || 0 }} ST + {{ scope.row.mx?.length || 0 }} MX</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('device.reportGroup.progress.title')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
    <template #footer>
      <div v-if="isUpload" style="margin-right: 5px; margin-bottom: 5px">
        <el-button @click="cancelUpload">{{ t("common.cancel") }}</el-button>
      </div>
    </template>
  </el-dialog>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>
<script setup lang="ts">
import { Delete, Refresh, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh, getTableMaxHeight } from "@/utils/index";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { IECNotify, RealEventState, ReportParam, ResultData } from "@/api";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { createVirtualTreeConfig } from "@/utils/performance/tableOptimization";
import { ref, computed, nextTick, onMounted, onBeforeUnmount, watch } from "vue";
import { useDebugStore } from "@/stores/modules/debug";
import { reportApi } from "@/api/modules/biz/debug/report";
import { realEventApi } from "@/api/modules/biz/debug/realevent";
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { osControlApi } from "@/api/modules/biz/os";
import { cloneDeep, isEmpty } from "lodash";
import { ElMessageBox } from "element-plus";
import { useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const progressDialog = ref();
const { t } = useI18n();
const realEventState = ref<RealEventState>({
  subscribe: false,
  type: ""
});
const refreshLoading = ref(false);
const { report, addConsole } = useDebugStore();
const { sysInfo } = useConfigStore();
const { paramInfo } = useConfigStore();
const filePath = ref("");
const globalReport = report.get(props.deviceId);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref(false);
const contextmenu = ref<ContextmenuInstance>();
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: ""
});
let currLine: any = undefined;
const cellContextmenu = (row: any, column?: any, event?: MouseEvent): void => {
  // 检查是否为第一级数据
  const isFirstLevel = isRootNode(row);

  if (!isFirstLevel) {
    // 如果不是第一级数据，不设置当前行，这样右键菜单就不会有有效的操作目标
    console.log("[右键菜单] 非第一级数据，不响应右键:", row);
    currLine = undefined;
    return;
  }

  currLine = row;
  console.log("[右键菜单] 第一级数据，设置当前行:", row);

  // 只有当第三个参数是真正的事件对象时才调用preventDefault
  if (event && typeof event === "object" && "preventDefault" in event) {
    event.preventDefault();
  }
};

// Tree V2 节点点击事件
const handleTreeNodeClick = (data: any) => {
  currLine = data;
};

// Tree V2 行右键菜单事件
const handleTreeRowContextMenu = (event: MouseEvent, data: any) => {
  event.preventDefault();
  event.stopPropagation();

  // 检查是否为第一级数据
  const isFirstLevel = isRootNode(data);

  if (!isFirstLevel) {
    // 如果不是第一级数据，不显示右键菜单
    console.log("[右键菜单] 非第一级数据，不显示菜单:", data);
    return;
  }

  currLine = data;
  console.log("[右键菜单] 第一级数据，显示菜单:", data);

  // 手动触发右键菜单显示
  if (contextmenu.value) {
    contextmenu.value.show(event);
  }
};

// 判断是否为根节点（第一级数据）
const isRootNode = (data: any): boolean => {
  // 根据数据结构判断是否为第一级节点
  if (!data) return false;

  // 根据后端 convertToTreeData 函数的逻辑，数据结构如下：
  // 第一级（根节点）: name 以 "Fault No: " 开头，id 为故障编号，有 children
  // 第二级（中间节点）: name 为 "Idx"，id 为故障编号+1001，有 children
  // 第三级（叶子节点）: name 为数字索引，没有 children

  // 1. 检查是否为第一级：name 以 "Fault No: " 开头
  const isFaultNoPattern = /^Fault No:\s*\d+$/i.test(data.name || "");

  // 2. 检查是否为第一级：id 小于 1000（故障编号通常小于1000，而第二级 id = 故障编号 + 1001）
  const isRootId = data.id !== undefined && data.id < 1000;

  // 3. 检查是否有子节点且子节点的 name 为 "Idx"
  const hasIdxChild =
    data.children && Array.isArray(data.children) && data.children.length > 0 && data.children[0]?.name === "Idx";

  // 满足以下任一条件即为第一级数据：
  // - name 匹配 "Fault No: 数字" 模式
  // - id 小于 1000 且有子节点
  // - 有名为 "Idx" 的子节点
  const isFirstLevel = isFaultNoPattern || (isRootId && data.children) || hasIdxChild;

  console.log(`[isRootNode] 数据: ${data.name}, id: ${data.id}, 是否第一级: ${isFirstLevel}`, {
    isFaultNoPattern,
    isRootId,
    hasIdxChild,
    hasChildren: !!data.children,
    childrenCount: data.children?.length || 0
  });

  return isFirstLevel;
};

// 隐藏右键菜单
const hideContextMenu = (event?: MouseEvent) => {
  // 如果是右键点击，不隐藏菜单
  if (event && event.button === 2) {
    return;
  }

  // 检查点击的目标是否是右键菜单本身
  if (event && event.target) {
    const target = event.target as HTMLElement;
    const contextMenuElement = document.querySelector(".v-contextmenu");
    if (contextMenuElement && contextMenuElement.contains(target)) {
      // 如果点击的是菜单内部，不隐藏菜单
      return;
    }
  }

  if (contextmenu.value) {
    contextmenu.value.hide();
  }
};
const showHiddenMark = ref<boolean>(false);
// showHiddenDesc 已移除，因为在模板中未使用
const isUpload = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<any[]>([]);
const refreshMark = ref<boolean>(false);
let uploadTimeoutId: NodeJS.Timeout | null = null;

// 录波上传进度管理
const uploadProgress = ref({
  totalFiles: 0, // 总文件数
  completedFiles: 0, // 已完成文件数
  currentFileProgress: 0, // 当前文件进度
  overallProgress: 0, // 总体进度
  currentFileName: "" // 当前文件名
});

// 计算总体进度
const calculateOverallProgress = () => {
  if (uploadProgress.value.totalFiles === 0) {
    uploadProgress.value.overallProgress = 0;
    return 0;
  }

  // 总体进度 = (已完成文件数 * 100 + 当前文件进度) / 总文件数
  const progress =
    (uploadProgress.value.completedFiles * 100 + uploadProgress.value.currentFileProgress) / uploadProgress.value.totalFiles;
  uploadProgress.value.overallProgress = Math.min(100, Math.max(0, progress));

  console.log(
    `[录波进度] 总文件: ${uploadProgress.value.totalFiles}, 已完成: ${uploadProgress.value.completedFiles}, 当前文件进度: ${uploadProgress.value.currentFileProgress}%, 总体进度: ${uploadProgress.value.overallProgress}%`
  );

  return uploadProgress.value.overallProgress;
};

// 重置上传进度
const resetUploadProgress = () => {
  uploadProgress.value.totalFiles = 0;
  uploadProgress.value.completedFiles = 0;
  uploadProgress.value.currentFileProgress = 0;
  uploadProgress.value.overallProgress = 0;
  uploadProgress.value.currentFileName = "";
};
const refreshName = computed(() =>
  refreshMark.value ? t("device.reportGroup.refresh.stop") : t("device.reportGroup.refresh.start")
);
const totalNum = computed(() => {
  return tableData.value.length;
});

// 判断是否为树形数据
const isTreeData = computed(() => {
  if (tableData.value.length === 0) return false;
  const firstItem = tableData.value[0];
  // 树形数据包含 id, name, value, ret_ms, children 字段
  // 平面数据包含 faultNo, faultStartTime, st, mx 字段
  return (
    firstItem &&
    typeof firstItem.id !== "undefined" &&
    typeof firstItem.name !== "undefined" &&
    typeof firstItem.value !== "undefined"
  );
});

// 判断是否应该使用虚拟化（数据量大于50条或总节点数大于200时使用）
const shouldUseVirtualization = computed(() => {
  if (!isTreeData.value) return false;

  const maxHeight = getTableMaxHeight(220);
  const containerHeight = typeof maxHeight === "number" ? maxHeight - 35 : 400;
  const config = createVirtualTreeConfig(tableData.value, 32, containerHeight);
  return config.enabled;
});

// Tree V2 的数据格式（确保数据结构符合 Tree V2 要求）
const treeTableData = computed(() => {
  if (!isTreeData.value) return [];

  // 添加调试信息
  if (process.env.NODE_ENV === "development") {
    console.log("[ReportGroup] Tree data count:", tableData.value.length);
    console.log("[ReportGroup] Should use virtualization:", shouldUseVirtualization.value);
  }

  return tableData.value;
});

// 默认展开的节点键值
const defaultExpandedKeys = computed(() => {
  if (!isTreeData.value) return [];

  // 递归收集所有节点的 id
  const collectAllIds = (nodes: any[]): string[] => {
    const ids: string[] = [];
    nodes.forEach(node => {
      if (node.id) {
        ids.push(node.id);
      }
      if (node.children && Array.isArray(node.children)) {
        ids.push(...collectAllIds(node.children));
      }
    });
    return ids;
  };

  return collectAllIds(tableData.value);
});

// Tree V2 的属性配置
const treeProps = {
  children: "children",
  label: "name"
};

// Tree V2 的高度计算 - 精确计算避免多余空间
const treeHeight = computed(() => {
  const maxHeight = getTableMaxHeight(220);
  const headerHeight = 35;
  // 精确计算高度，避免多余空间
  if (typeof maxHeight === "number" && maxHeight > headerHeight) {
    return maxHeight - headerHeight;
  }
  return 480; // 默认高度
});

// 表格固定配置，确保布局稳定
const tableFixedConfig = computed(() => ({
  // 固定表头高度
  headerHeight: 35,
  // 固定列宽配置
  columnWidths: {
    reportId: 240,
    time: 300
  },
  // 表格布局固定
  tableLayout: "fixed"
}));

const getTableData = (): any[] => {
  return globalReport!.groupReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange:
      dateRange.value && dateRange.value.length >= 2
        ? [new Date(dateRange.value[0]), new Date(dateRange.value[1])]
        : getDefaultDateRange(),
    searchInfo: "",
    searchType: 0
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
  }
};
const uploadWave = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("device.reportGroup.messages.noFileToUpload"));
    return;
  }

  let fileNo = getTableData()[0]?.name;
  if (currLine && currLine.name && currLine.name.includes(":")) {
    const no = currLine.name.split(":")[1];
    fileNo = no ? no.replace(" ", "") : undefined;
  }
  const arg = { fileNo: fileNo, time: currLine?.value };

  // 清除之前的超时定时器
  if (uploadTimeoutId) {
    clearTimeout(uploadTimeoutId);
    uploadTimeoutId = null;
  }

  // 重置并初始化进度条
  resetUploadProgress();
  isUpload.value = true;
  dialogShow.value.searchProgress = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.progressText = t("device.reportGroup.messages.waveFileUploading");

  console.log(`[录波上传] 开始上传 - 文件编号: ${fileNo}, 时间: ${currLine?.value}`);

  // 设置超时处理（30秒后自动取消）
  uploadTimeoutId = setTimeout(() => {
    if (isUpload.value) {
      isUpload.value = false;
      dialogShow.value.searchProgress = false;
      Message.warning(t("device.reportGroup.messages.waveFileUploadTimeout"));
      console.warn("录波上传超时");
    }
  }, 30000);

  try {
    const res = await reportApi.uploadWaveByDevice(props.deviceId, arg);

    // 清除超时定时器
    if (uploadTimeoutId) {
      clearTimeout(uploadTimeoutId);
      uploadTimeoutId = null;
    }

    if (res.code != 1) {
      // 上传失败，隐藏进度条
      resetUploadProgress();
      isUpload.value = false;
      dialogShow.value.searchProgress = false;
      Message.warning(res.msg);
      return;
    }
    filePath.value = res.data;
  } catch (error) {
    // 清除超时定时器
    if (uploadTimeoutId) {
      clearTimeout(uploadTimeoutId);
      uploadTimeoutId = null;
    }

    // 上传异常，隐藏进度条
    resetUploadProgress();
    isUpload.value = false;
    dialogShow.value.searchProgress = false;
    Message.error(t("device.reportGroup.messages.waveFileUploadError"));
    console.error("录波上传异常:", error);
  }
};

// 取消录波上传
const cancelUpload = (): void => {
  try {
    // 清除超时定时器
    if (uploadTimeoutId) {
      clearTimeout(uploadTimeoutId);
      uploadTimeoutId = null;
    }

    // 重置上传状态
    resetUploadProgress();
    isUpload.value = false;
    dialogShow.value.searchProgress = false;
    dialogShow.value.percentage = 0;
    dialogShow.value.progressText = "";

    // 尝试调用后端取消接口（如果失败也不影响前端状态重置）
    reportApi
      .cancelUploadByDevice(props.deviceId, undefined)
      .then(res => {
        if (res.code === 1) {
          Message.info(t("device.reportGroup.messages.waveFileUploadCancelled") || "录波上传已取消");
          addConsole("录波上传已被用户取消");
        } else {
          console.warn("取消录波上传接口返回错误:", res.msg);
          Message.info(t("device.reportGroup.messages.waveFileUploadCancelled") || "录波上传已取消");
        }
      })
      .catch(error => {
        console.warn("调用取消录波上传接口失败:", error);
        // 即使接口调用失败，也显示取消成功（因为前端状态已重置）
        Message.info(t("device.reportGroup.messages.waveFileUploadCancelled") || "录波上传已取消");
      });
  } catch (error) {
    console.error("取消录波上传异常:", error);
    // 确保状态被重置
    resetUploadProgress();
    isUpload.value = false;
    dialogShow.value.searchProgress = false;
    dialogShow.value.percentage = 0;
    Message.info(t("device.reportGroup.messages.waveFileUploadCancelled") || "录波上传已取消");
  }
};
let timerId: NodeJS.Timeout | null = null;
const timerRefresh = (): void => {
  timerId = setTimeout(() => {
    refreshList().then(() => {
      timerRefresh();
    });
  }, paramInfo.REPORT_REFRESH_TIME);
};

const refreshReport = (): void => {
  refreshMark.value = !refreshMark.value;
  if (refreshMark.value == true) {
    subRealEvent();
  } else {
    unSubRealEvent();
  }
};

const clearList = (): void => {
  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("device.reportGroup.messages.selectDateRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcGroupReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[0])) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[1])) : "",
    faultAfter: ""
  };
  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  isUpload.value = false;
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("device.reportGroup.progress.searching", { type: reportDesc });
  startFakeProgress();
  // 记录开始时间
  const start = Date.now();
  const res: ResultData<any> = await reportApi.getGroupReportListByDevice(props.deviceId, arg);
  // 计算已用时，若不足500ms则补足
  const elapsed = Date.now() - start;
  if (elapsed < 500) {
    await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
  }
  stopFakeProgress();
  dialogShow.value.searchProgress = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    tableLoad.value = false;
    isButtonClick.value = false;
    return;
  }
};

const refreshList = async (): Promise<void> => {
  const arg: ReportParam.IECRpcGroupReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: "",
    stopTime: "",
    faultAfter: ""
  };
  const res = await reportApi.refreshGroupReportByDevice(props.deviceId, arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }

  console.log("[ReportGroup] refreshGroupReport 返回数据:", res.data);
  console.log("[ReportGroup] 数据类型:", typeof res.data, "是否为数组:", Array.isArray(res.data));
  if (Array.isArray(res.data) && res.data.length > 0) {
    console.log("[ReportGroup] 第一条数据结构:", res.data[0]);
    console.log("[ReportGroup] 第一条数据字段:", Object.keys(res.data[0]));
  }

  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  if (Array.isArray(res.data)) {
    let filteredList = res.data as any;
    if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
      const keyword = globalReport!.keyword.toLowerCase();
      filteredList = res.data.filter(item => {
        // 判断数据类型并使用相应的字段进行过滤
        if (item.id !== undefined && item.name !== undefined) {
          // 树形数据：搜索 name, value, ret_ms 字段
          const name = (item.name || "").toString().toLowerCase();
          const value = (item.value || "").toString().toLowerCase();
          const ret_ms = (item.ret_ms || "").toString().toLowerCase();
          return name.includes(keyword) || value.includes(keyword) || ret_ms.includes(keyword);
        } else {
          // 平面数据：搜索 faultNo, faultStartTime 字段
          const faultNo = (item.faultNo || "").toString().toLowerCase();
          const faultStartTime = (item.faultStartTime || "").toLowerCase();
          return faultNo.includes(keyword) || faultStartTime.includes(keyword);
        }
      });
    }

    console.log("[ReportGroup] 设置表格数据:", filteredList);
    console.log("[ReportGroup] 过滤后数据量:", filteredList.length);
    if (filteredList.length > 0) {
      console.log("[ReportGroup] 第一条过滤后数据:", filteredList[0]);
    }

    tableData.value = filteredList;
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

    console.log("[ReportGroup] tableData.value 设置完成，长度:", tableData.value.length);
    console.log("[ReportGroup] isTreeData 计算结果:", isTreeData.value);
  } else {
    console.log("[ReportGroup] 没有数据或数据不是数组");
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    tableData.value = [];
  }
};

let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.value.percentage = 0;
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.value.percentage < 95) {
      dialogShow.value.percentage += 5;
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.value.percentage = 100;
}

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.group.messages.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.group.messages.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.group.messages.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [{ name: "Rpt", extensions: ["rpt"] }]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  progressDialog.value.setProgress(5, t("report.exporting"), false);
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      name: obj.name,
      value: obj.value,
      ret_ms: obj.ret_ms,
      children: obj.children
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: cloneDeep(exportList)
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.group.messages.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.group.messages.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm"),
    type: "success"
  }).catch(() => {
    // 用户点击关闭按钮取消操作，不需要处理
  });
};

const initTableData = (): void => {
  const cachedData = getTableData();
  if (cachedData.length > 0) {
    // 如果有缓存数据，先显示缓存数据
    dialogShow.value.percentage = 50;
    dialogShow.value.searchProgress = true;
    dialogShow.value.progressText = t("device.reportGroup.progress.loading");
    nextTick(() => {
      setTimeout(() => {
        tableData.value = cachedData;
        dialogShow.value.percentage = 100;
        dialogShow.value.searchProgress = false;
      }, 50);
    });
  } else {
    // 如果没有缓存数据，主动获取树形数据
    refreshList();
  }
};

const openWaveFile = (deviceId: string, path: string) => {
  ElMessageBox.confirm(t("device.reportGroup.messages.openWaveFileConfirm"), t("device.reportGroup.messages.openWaveFileTitle"), {
    confirmButtonText: t("device.reportGroup.messages.confirm"),
    cancelButtonText: t("device.reportGroup.messages.cancel"),
    type: "success"
  }).then(async () => {
    if (isEmpty(sysInfo.WAVE_TOOL_PATH)) {
      Message.warning(t("device.reportGroup.messages.waveToolNotConfigured"));
      addConsole(t("device.reportGroup.messages.waveToolNotConfigured"));
      return;
    }
    const exePath = sysInfo.WAVE_TOOL_PATH;
    const filePath = path;
    const arg = { exePath: exePath, filePath: filePath };
    const res: ResultData<any> = await reportApi.openWaveFile(deviceId, arg);
    if (res.code != 1) {
      Message.warning(res.msg);
      return;
    }
  });
};

const notifyMethod = async (_event: unknown, notify: IECNotify): Promise<void> => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;
  if (notify.type == "readGroupReport") {
    console.log(
      `[GroupReport UI] 处理故障报告通知 - isPartial: ${notify.isPartial}, 数据类型: ${reportData.data?.progress ? "进度" : "数据"}, 当前界面状态: isReportLoading=${globalReport!.isReportLoading}, searchProgress=${dialogShow.value.searchProgress}`
    );

    if (reportData.code != 1) {
      // 错误处理
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
      Message.warning(reportData.msg);
      console.log(`[GroupReport UI] 处理错误完成`);
      return;
    }
    // 检查是否为进度更新
    if (reportData.data?.progress?.isProgress) {
      // 这是进度更新，只更新进度条，不更新表格数据
      const progressInfo = reportData.data.progress;
      const currentCount = progressInfo.currentCount;
      const callbackCount = progressInfo.callbackCount;

      // 基于实际数据量计算进度
      let progressPercent = 0;
      if (currentCount > 0) {
        // 使用对数函数计算进度，避免进度跳跃过快
        progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
      } else {
        progressPercent = Math.min(20, callbackCount * 3);
      }

      // 确保进度不倒退
      progressPercent = Math.max(dialogShow.value.percentage, progressPercent);

      dialogShow.value.percentage = progressPercent;

      console.log(
        `[GroupReport UI] 进度更新完成 - 数据量: ${currentCount}条, 回调次数: ${callbackCount}, 进度: ${progressPercent}%`
      );
      return;
    }

    // 这是最终数据更新
    if (Array.isArray(reportData.data)) {
      console.log(`[GroupReport UI] 处理最终数据 - 原始数据量: ${reportData.data.length}条`);
      console.log(`[GroupReport UI] 数据样本:`, reportData.data.slice(0, 2)); // 显示前2条数据样本
      console.log(`[GroupReport UI] 当前关键字:`, globalReport!.keyword);

      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        const keyword = globalReport!.keyword.toLowerCase();
        filteredList = reportData.data.filter(item => {
          // 判断数据类型并使用相应的字段进行过滤
          if (item.id !== undefined && item.name !== undefined) {
            // 树形数据：搜索 name, value, ret_ms 字段
            const name = (item.name || "").toString().toLowerCase();
            const value = (item.value || "").toString().toLowerCase();
            const ret_ms = (item.ret_ms || "").toString().toLowerCase();
            const matches = name.includes(keyword) || value.includes(keyword) || ret_ms.includes(keyword);
            if (!matches && reportData.data.length <= 5) {
              console.log(`[GroupReport UI] 过滤项目: "${item.name} ${item.value}" 不匹配关键字 "${globalReport!.keyword}"`);
            }
            return matches;
          } else {
            // 平面数据：搜索 faultNo 和 faultStartTime 字段
            const faultNo = (item.faultNo || "").toString().toLowerCase();
            const faultStartTime = (item.faultStartTime || "").toLowerCase();
            const matches = faultNo.includes(keyword) || faultStartTime.includes(keyword);
            if (!matches && reportData.data.length <= 5) {
              console.log(
                `[GroupReport UI] 过滤项目: "故障${item.faultNo} ${item.faultStartTime}" 不匹配关键字 "${globalReport!.keyword}"`
              );
            }
            return matches;
          }
        });
        console.log(`[GroupReport UI] 数据过滤完成 - 原始数据: ${reportData.data.length}条, 过滤后: ${filteredList.length}条`);
      }

      console.log(
        `[GroupReport UI] 准备渲染数据 - tableData当前长度: ${tableData.value.length}, 新数据长度: ${filteredList.length}`
      );

      // 最终数据直接渲染到表格
      tableData.value = filteredList;
      globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

      console.log(`[GroupReport UI] 数据已设置 - tableData.value.length: ${tableData.value.length}`);
      console.log(`[GroupReport UI] 表格数据样本:`, tableData.value.slice(0, 2));

      // 强制触发Vue响应式更新
      nextTick(() => {
        console.log(`[GroupReport UI] nextTick后 - tableData.value.length: ${tableData.value.length}`);
        console.log(`[GroupReport UI] DOM更新后的表格状态检查完成`);

        // 验证数据字段
        if (tableData.value.length > 0) {
          const firstItem = tableData.value[0] as any;
          console.log(`[GroupReport UI] 第一条数据字段检查:`);
          if (firstItem.id !== undefined && firstItem.name !== undefined) {
            // 树形数据
            console.log(`  - 数据类型: 树形数据`);
            console.log(`  - id: "${firstItem.id}" (${typeof firstItem.id})`);
            console.log(`  - name: "${firstItem.name}" (${typeof firstItem.name})`);
            console.log(`  - value: "${firstItem.value}" (${typeof firstItem.value})`);
            console.log(`  - ret_ms: "${firstItem.ret_ms}" (${typeof firstItem.ret_ms})`);
            console.log(`  - children: ${firstItem.children?.length || 0}条 (${typeof firstItem.children})`);
          } else {
            // 平面数据
            console.log(`  - 数据类型: 平面数据`);
            console.log(`  - faultNo: "${firstItem.faultNo}" (${typeof firstItem.faultNo})`);
            console.log(`  - faultStartTime: "${firstItem.faultStartTime}" (${typeof firstItem.faultStartTime})`);
            console.log(`  - st: ${firstItem.st?.length || 0}条 (${typeof firstItem.st})`);
            console.log(`  - mx: ${firstItem.mx?.length || 0}条 (${typeof firstItem.mx})`);
          }
        }
      });

      // 完成加载
      globalReport!.isReportLoading = false;
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[GroupReport UI] 最终数据处理完成 - 最终数据量: ${filteredList.length}条`);
    } else {
      // 空数据情况
      console.log(`[GroupReport UI] 处理空数据情况`);
      globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];

      globalReport!.isReportLoading = false;
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[GroupReport UI] 空数据处理完成`);
    }
    return;
  }
  if (notify.type == "fileUpload") {
    // 只有在上传状态时才更新进度
    if (isUpload.value) {
      dialogShow.value.searchProgress = true;

      // 从后端获取的进度数据
      const progressData = reportData.data || reportData;
      const fileName = progressData.fileItem?.fileName || "";

      // 如果是新文件开始传输，更新总文件数
      if (progressData.status === "START" || (progressData.percentage === 0 && fileName)) {
        // 检查是否是新文件
        const isNewFile = !uploadProgress.value.currentFileName || uploadProgress.value.currentFileName !== fileName;
        if (isNewFile) {
          if (uploadProgress.value.totalFiles === 0) {
            // 第一次获取文件信息时，估算总文件数（通常录波文件包含.cfg, .dat等多个文件）
            uploadProgress.value.totalFiles = progressData.totalFiles || 3; // 默认估算3个文件
          }
          uploadProgress.value.currentFileName = fileName;
          console.log(`[录波上传] 开始传输新文件: ${fileName}, 总文件数: ${uploadProgress.value.totalFiles}`);
        }
      }

      // 更新当前文件进度
      let currentFileProgress = 0;
      if (progressData.percentage !== undefined) {
        currentFileProgress = Math.max(0, Math.min(100, progressData.percentage));
      } else if (progressData.status) {
        // 根据状态估算进度
        switch (progressData.status) {
          case "START":
            currentFileProgress = 0;
            break;
          case "TRANSFERRING":
            currentFileProgress = 50;
            break;
          case "COMPLETE":
            currentFileProgress = 100;
            break;
          default:
            currentFileProgress = uploadProgress.value.currentFileProgress;
        }
      }

      // 如果当前文件完成，增加已完成文件数
      if (currentFileProgress === 100 && uploadProgress.value.currentFileProgress < 100) {
        uploadProgress.value.completedFiles++;
        console.log(`[录波上传] 文件完成: ${fileName}, 已完成文件数: ${uploadProgress.value.completedFiles}`);
      }

      uploadProgress.value.currentFileProgress = currentFileProgress;

      // 计算并更新总体进度
      const overallProgress = calculateOverallProgress();
      dialogShow.value.percentage = overallProgress;

      // 显示当前传输的文件信息
      dialogShow.value.progressText = fileName
        ? `${t("device.reportGroup.messages.waveFileUploading")}: ${fileName} (${uploadProgress.value.completedFiles + 1}/${uploadProgress.value.totalFiles})`
        : t("device.reportGroup.messages.waveFileUploading");

      console.log(`[录波上传] 文件进度更新: ${fileName} ${currentFileProgress}%, 总体进度: ${overallProgress}%`);
    }
    return;
  }
  if (notify.type == "fileUploadSuccess") {
    // 清除超时定时器
    if (uploadTimeoutId) {
      clearTimeout(uploadTimeoutId);
      uploadTimeoutId = null;
    }

    // 完成上传 - 确保显示100%进度
    uploadProgress.value.completedFiles = uploadProgress.value.totalFiles;
    uploadProgress.value.currentFileProgress = 100;
    uploadProgress.value.overallProgress = 100;

    isUpload.value = false;
    dialogShow.value.percentage = 100;

    // 延迟隐藏进度条，让用户看到100%的进度
    setTimeout(() => {
      dialogShow.value.searchProgress = false;
      resetUploadProgress(); // 延迟重置进度，确保用户看到完成状态
    }, 500);

    dialogShow.value.progressText = t("device.reportGroup.messages.waveFileUploadComplete") || "录波上传完成";
    Message.success(t("device.reportGroup.messages.waveFileUploadComplete") || "录波上传完成");

    const filePath = reportData.data || reportData;
    console.log(`[录波上传] 上传成功，文件路径: ${filePath}`);

    openWaveFile(props.deviceId, filePath);
    addConsole(`录波上传完成，文件路径: ${filePath}`);
    return;
  }
  if (notify.type == "fileUploadError") {
    // 清除超时定时器
    if (uploadTimeoutId) {
      clearTimeout(uploadTimeoutId);
      uploadTimeoutId = null;
    }

    // 上传失败
    resetUploadProgress();
    isUpload.value = false;
    dialogShow.value.searchProgress = false;
    dialogShow.value.percentage = 0;

    const errorMsg =
      reportData.data?.msg || reportData.msg || t("device.reportGroup.messages.waveFileUploadError") || "录波上传失败";
    const fileName = reportData.data?.fileItem?.fileName || "";

    Message.error(fileName ? `${errorMsg}: ${fileName}` : errorMsg);
    addConsole(`录波上传失败: ${errorMsg}${fileName ? ` (文件: ${fileName})` : ""}`);

    console.error(`[录波上传] 上传失败:`, reportData);
    return;
  }
};

onMounted(() => {
  ipc.on("report_notify", notifyMethod);
  initTableData();
  // 恢复查询条件
  restoreQueryConditions();

  // 添加全局点击监听器来隐藏右键菜单
  document.addEventListener("click", handleGlobalClick);
  document.addEventListener("contextmenu", handleGlobalRightClick);
});

// 全局点击处理
const handleGlobalClick = (event: MouseEvent) => {
  // 检查点击的目标是否是右键菜单
  const target = event.target as HTMLElement;
  const contextMenuElement = document.querySelector(".v-contextmenu");

  if (contextMenuElement && !contextMenuElement.contains(target)) {
    // 如果点击的不是菜单内部，隐藏菜单
    if (contextmenu.value) {
      contextmenu.value.hide();
    }
  }
};

// 全局右键点击处理
const handleGlobalRightClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const reportPageElement = document.querySelector(".report-page");

  // 如果右键点击的不是报告页面内的元素，隐藏菜单
  if (reportPageElement && !reportPageElement.contains(target)) {
    if (contextmenu.value) {
      contextmenu.value.hide();
    }
  }
};

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  if (uploadTimeoutId) {
    clearTimeout(uploadTimeoutId);
    uploadTimeoutId = null;
  }

  // 移除全局事件监听器
  document.removeEventListener("click", handleGlobalClick);
  document.removeEventListener("contextmenu", handleGlobalRightClick);

  ipc.removeAllListeners("report_notify");
});

watch(
  () => refreshMark.value,
  newValue => {
    if (newValue == true) {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = true;
      timerRefresh();
    } else {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = false;
      if (timerId) {
        clearTimeout(timerId);
      }
    }
  }
);
watch(
  () => showHiddenMark.value,
  () => {
    // 清空缓存
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    refreshList();
  }
);
watch(
  () => globalReport!.newname,
  () => {
    const cachedData = getTableData();
    if (cachedData.length > 0) {
      // 如果有缓存数据，先显示缓存数据
      tableData.value = cachedData;
    } else {
      // 如果没有缓存数据，主动获取树形数据
      refreshList();
    }

    isButtonClick.value = false;
    refreshMark.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();

    // refreshName现在是computed属性，会自动更新
    if (timerId) {
      clearTimeout(timerId);
    }
    unSubRealEvent();
  }
);
const subRealEvent = async () => {
  if (realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 订阅事件
    const res = await realEventApi.subRealEventByDevice(props.deviceId, [globalReport!.currReportType]);
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = true;
      realEventState.value.type = globalReport!.currReportType;
    }
  } finally {
    refreshLoading.value = false;
  }
};
const unSubRealEvent = async () => {
  if (!realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 取消订阅
    const res = await realEventApi.unSubRealEventByDevice(props.deviceId, { type: [realEventState.value.type] });
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = false;
      realEventState.value.type = "";
    }
  } finally {
    refreshLoading.value = false;
  }
};
onUnmounted(() => {
  unSubRealEvent();
});
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
    flex: 1;
    display: flex;
    flex-direction: column;

    // 确保普通表格的表头也保持固定
    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-table__header {
          table-layout: fixed; // 固定表格布局

          th {
            // 防止表头列宽自动调整
            &.el-table__cell {
              padding: 8px 0;
              height: 35px; // 与虚拟化表头保持一致的高度

              .cell {
                padding: 0 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }

      // 确保表格内容列宽与表头一致
      .el-table__body-wrapper {
        .el-table__body {
          table-layout: fixed;

          td {
            &.el-table__cell {
              .cell {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
}

.header {
  margin-bottom: 5px;
}

// 虚拟化树形组件样式
.virtualized-tree-container {
  border: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;

  .tree-table-header {
    display: flex;
    align-items: center;
    height: 35px;
    background: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color);
    font-weight: bold;
    color: var(--el-text-color-primary);

    // 固定表头，防止数据加载后变动
    &.fixed-header {
      min-height: 35px;
      max-height: 35px;
      flex-shrink: 0;
    }

    .header-cell {
      padding: 0 12px;
      border-right: 1px solid var(--el-border-color);
      display: flex;
      align-items: center;
      overflow: hidden;
      flex-shrink: 0; // 防止收缩

      &:last-child {
        border-right: none;
      }

      &.flex-1 {
        flex: 1;
        min-width: 0; // 允许flex项目收缩
      }

      // 固定宽度类
      &.fixed-width-240 {
        width: 240px;
        min-width: 240px;
        max-width: 240px;
      }

      &.fixed-width-300 {
        width: 300px;
        min-width: 300px;
        max-width: 300px;
      }
    }
  }

  .tree-table-row {
    display: flex;
    align-items: center;
    height: 32px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    .tree-cell {
      padding: 0 8px;
      border-right: 1px solid var(--el-border-color-lighter);
      display: flex;
      align-items: center;
      overflow: hidden;
      flex-shrink: 0; // 防止收缩

      &:last-child {
        border-right: none;
      }

      &.flex-1 {
        flex: 1;
        min-width: 0; // 允许flex项目收缩
      }

      // 固定宽度类，与表头保持一致
      &.fixed-width-240 {
        width: 240px;
        min-width: 240px;
        max-width: 240px;
      }

      &.fixed-width-300 {
        width: 300px;
        min-width: 300px;
        max-width: 300px;
      }

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
      }
    }
  }

  // 覆盖 Element Plus Tree V2 的默认样式
  :deep(.el-tree-v2) {
    flex: 1;
    min-height: 0; // 防止 flex 子元素溢出

    .el-tree-v2__node {
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
    }

    .el-tree-v2__node-content {
      padding: 0;
      height: 32px;
    }
  }
}
</style>
