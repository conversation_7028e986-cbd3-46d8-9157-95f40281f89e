# 远程控制功能使用说明

## 功能概述

远程控制功能允许您通过VNC协议连接到远程计算机，实现远程桌面控制。该功能基于noVNC技术，提供了完整的远程桌面体验。

## 如何使用

### 1. 打开远程控制

1. 点击应用顶部工具栏的"更多"按钮（网格图标）
2. 在弹出的菜单中选择"远程控制"选项

### 2. 配置连接参数

在连接配置界面中填写以下信息：

- **主机地址**: 目标VNC服务器的IP地址或主机名
  - 例如: `*************` 或 `server.example.com`
- **端口**: VNC服务器端口号
  - 默认: `5900`
  - 如果是第二个显示器，通常是 `5901`
- **用户名**: VNC服务器用户名（可选）
  - 某些VNC服务器需要用户名验证
- **密码**: VNC服务器密码（可选）
  - 大多数VNC服务器需要密码验证

### 3. 建立连接

1. 填写完连接信息后，点击"连接"按钮
2. 系统会先测试连接可达性
3. 然后建立VNC连接
4. 连接成功后会显示远程桌面画面

### 4. 远程操作

连接成功后，您可以：

#### 基本操作
- **鼠标控制**: 在显示区域内移动鼠标，点击操作
- **键盘输入**: 点击显示区域获得焦点后，可以进行键盘输入
- **滚动**: 使用鼠标滚轮进行页面滚动

#### 工具栏功能
- **Ctrl+Alt+Del**: 发送Ctrl+Alt+Del组合键到远程系统
- **全屏**: 切换全屏显示模式
- **截图**: 保存当前远程桌面截图
- **断开连接**: 断开VNC连接

### 5. 断开连接

1. 点击工具栏中的"断开连接"按钮
2. 确认断开连接
3. 系统会清理连接资源并关闭显示

## 功能特性

### 显示特性
- **自动缩放**: 远程桌面会自动缩放以适应显示窗口
- **高质量渲染**: 基于HTML5 Canvas的高质量图像渲染
- **实时更新**: 远程桌面画面实时更新

### 交互特性
- **完整鼠标支持**: 支持左键、右键、中键和滚轮
- **完整键盘支持**: 支持所有键盘按键和组合键
- **剪贴板同步**: 支持本地和远程剪贴板同步（取决于VNC服务器）

### 连接特性
- **连接状态监控**: 实时显示连接状态
- **自动重连**: 连接断开时自动尝试重连
- **错误处理**: 完善的错误提示和处理机制

## 支持的VNC服务器

### Windows
- TightVNC Server
- UltraVNC Server
- RealVNC Server
- Windows内置远程桌面（需要VNC包装器）

### Linux
- x11vnc
- TigerVNC Server
- TightVNC Server
- Vino (GNOME)

### macOS
- 内置屏幕共享
- RealVNC Server
- TightVNC Server

## 故障排除

### 连接问题

**问题**: 无法连接到VNC服务器
**解决方案**:
1. 检查网络连通性：`ping [服务器IP]`
2. 检查端口是否开放：`telnet [服务器IP] 5900`
3. 确认VNC服务器正在运行
4. 检查防火墙设置

**问题**: 连接超时
**解决方案**:
1. 增加网络超时时间
2. 检查网络稳定性
3. 尝试使用有线网络连接

### 认证问题

**问题**: 身份验证失败
**解决方案**:
1. 确认VNC服务器密码正确
2. 检查用户名是否必需
3. 确认VNC服务器认证设置

### 性能问题

**问题**: 画面更新缓慢
**解决方案**:
1. 检查网络带宽
2. 降低VNC服务器图像质量设置
3. 启用VNC压缩功能

**问题**: 鼠标或键盘响应延迟
**解决方案**:
1. 检查网络延迟
2. 关闭不必要的网络应用
3. 使用有线网络连接

## 安全注意事项

### 网络安全
- 仅在可信网络环境中使用VNC连接
- 考虑使用VPN建立安全隧道
- 避免在公共网络上使用未加密的VNC连接

### 访问控制
- 使用强密码保护VNC服务器
- 限制VNC服务器的访问IP范围
- 定期更换VNC访问密码

### 数据保护
- 避免在VNC会话中处理敏感数据
- 使用后及时断开连接
- 定期检查VNC服务器日志

## 技术规格

### 支持的协议
- RFB (Remote Framebuffer) 3.3, 3.7, 3.8
- WebSocket over TCP
- 支持VNC认证和无认证模式

### 系统要求
- 现代Web浏览器（支持WebSocket和Canvas）
- 网络连接到VNC服务器
- 足够的带宽用于图像传输

### 性能建议
- 推荐带宽: 1Mbps以上
- 推荐延迟: 100ms以下
- 支持的分辨率: 最高4K（取决于网络性能）