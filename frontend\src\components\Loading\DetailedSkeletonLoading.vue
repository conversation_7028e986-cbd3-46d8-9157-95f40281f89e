<template>
  <div class="skeleton-loading">
    <div class="skeleton-header">
      <div class="skeleton-line skeleton-title"></div>
      <div class="skeleton-line skeleton-subtitle"></div>
    </div>
    <div class="skeleton-content">
      <div class="skeleton-line skeleton-text"></div>
      <div class="skeleton-line skeleton-text"></div>
      <div class="skeleton-line skeleton-text short"></div>
    </div>
    <div class="skeleton-actions">
      <div class="skeleton-button"></div>
      <div class="skeleton-button"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 详细骨架屏加载组件
</script>

<style scoped lang="scss">
.skeleton-loading {
  max-width: 600px;
  padding: 20px;
  margin: 0 auto;
}
.skeleton-header {
  margin-bottom: 24px;
}
.skeleton-content {
  margin-bottom: 24px;
}
.skeleton-actions {
  display: flex;
  gap: 12px;
}
.skeleton-line {
  height: 16px;
  margin-bottom: 12px;
  background: linear-gradient(
    90deg,
    var(--el-fill-color-light) 25%,
    var(--el-fill-color-lighter) 50%,
    var(--el-fill-color-light) 75%
  );
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite;
  &.skeleton-title {
    width: 60%;
    height: 24px;
  }
  &.skeleton-subtitle {
    width: 40%;
    height: 18px;
  }
  &.skeleton-text {
    width: 100%;
    &.short {
      width: 70%;
    }
  }
}
.skeleton-button {
  width: 80px;
  height: 32px;
  background: linear-gradient(
    90deg,
    var(--el-fill-color-light) 25%,
    var(--el-fill-color-lighter) 50%,
    var(--el-fill-color-light) 75%
  );
  background-size: 200% 100%;
  border-radius: 6px;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 暗色主题适配 */
html.dark {
  .skeleton-line,
  .skeleton-button {
    background: linear-gradient(
      90deg,
      var(--el-fill-color-darker) 25%,
      var(--el-fill-color-dark) 50%,
      var(--el-fill-color-darker) 75%
    );
    background-size: 200% 100%;
  }
}
</style>
