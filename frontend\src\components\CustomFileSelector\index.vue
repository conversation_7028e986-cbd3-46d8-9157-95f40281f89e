<template>
  <div class="custom-file-selector">
    <el-dialog v-model="dialogVisible" width="60%" :before-close="handleClose" draggable>
      <template #title>
        <div class="dialog-title">
          <el-icon class="dialog-title-icon">
            <Folder />
          </el-icon>
          <span class="dialog-title-text">{{ t("common.customFileSelector.title") }}</span>
        </div>
      </template>
      <div class="selector-container">
        <!-- 文件系统树形结构 -->
        <div class="file-tree-container">
          <div class="quick-folders" style="display: flex; gap: 8px; margin-bottom: 8px">
            <el-tooltip content="磁盘根目录" placement="bottom">
              <el-button size="small" type="info" @click="handleGoRoot" circle>
                <el-icon>
                  <Folder />
                </el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip v-for="folder in quickFolders" :key="folder.path" :content="folder.name" placement="bottom">
              <el-button
                size="small"
                @click="jumpToQuickFolder(folder)"
                circle
                :type="activeQuickFolderPath === folder.path ? 'primary' : 'default'"
              >
                <el-icon>
                  <component :is="getFolderIcon(folder.key)" />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
          <div class="tree-header">
            <el-input
              v-model="searchKeyword"
              :placeholder="t('common.customFileSelector.searchPlaceholder')"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
            <div v-if="loading" class="loading-indicator" style="margin-top: 8px; display: flex; align-items: center">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <span style="margin-left: 4px; font-size: 12px; color: #666">{{ t("common.customFileSelector.loading") }}</span>
            </div>
          </div>
          <div class="tree-content">
            <div v-if="loading" class="loading-state">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              {{ t("common.customFileSelector.loading") }}
            </div>
            <div v-else-if="errorMessage" class="error-state">
              <el-icon>
                <Warning />
              </el-icon>
              <span>{{ errorMessage }}</span>
              <el-button size="small" type="primary" @click="retryLoad" style="margin-left: 8px">重试</el-button>
            </div>
            <el-tree
              v-else
              ref="fileTreeRef"
              :data="fileTreeData"
              :props="treeProps"
              :load="loadNode"
              lazy
              node-key="path"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              @check="handleNodeCheck"
              show-checkbox
              check-strictly
            >
              <template #default="{ node, data }">
                <div class="tree-node" style="min-width: max-content">
                  <el-icon v-if="data.isDirectory" class="folder-icon">
                    <FolderOpened />
                  </el-icon>
                  <el-icon v-else class="file-icon">
                    <Document />
                  </el-icon>
                  <el-tooltip :content="node.label" placement="top" effect="dark" :show-after="500" :hide-after="0">
                    <span class="node-label">{{ node.label }}</span>
                  </el-tooltip>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 已选择的项目 -->
        <div class="selected-items-container">
          <div class="selected-header">
            <h4>{{ t("common.customFileSelector.selectedItems") }}</h4>
            <el-button type="danger" size="small" @click="clearSelected" :disabled="selectedItems.length === 0">
              {{ t("common.customFileSelector.clearAll") }}
            </el-button>
          </div>
          <div class="selected-content">
            <div v-for="item in selectedItems" :key="item.path" class="selected-item">
              <el-icon v-if="item.isDirectory" class="folder-icon">
                <FolderOpened />
              </el-icon>
              <el-icon v-else class="file-icon">
                <Document />
              </el-icon>
              <span class="item-name">{{ item.name }}</span>
              <span class="item-path">{{ item.path }}</span>
              <span class="item-size">{{ item.size ? MathUtils.formatBytes(item.size) : "" }}</span>
              <el-button type="danger" size="small" circle @click="removeSelectedItem(item)">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
            <div v-if="selectedItems.length === 0" class="empty-state">
              {{ t("common.customFileSelector.noItemsSelected") }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            {{ t("common.customFileSelector.cancel") }}
          </el-button>
          <el-button type="primary" @click="handleConfirm" :disabled="selectedItems.length === 0">
            {{ t("common.customFileSelector.confirm") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  Search,
  Folder,
  FolderOpened,
  Document,
  Close,
  Loading,
  Warning,
  Download,
  Picture,
  VideoCamera,
  Headset,
  Monitor
} from "@element-plus/icons-vue";
import { osControlApi } from "@/api/modules/biz/os";
import { MathUtils } from "@/utils/mathUtils";

interface FileItem {
  path: string;
  name: string;
  isDirectory: boolean;
  size?: number;
  modifiedAt?: Date;
  sizeNote?: string;
}

interface TreeNode {
  path: string;
  name: string;
  isDirectory: boolean;
  children?: TreeNode[];
  leaf?: boolean;
  modifiedAt?: Date;
  createdAt?: Date;
  accessedAt?: Date;
  size?: number;
}

interface SystemFolder {
  name: string;
  path: string;
  key: string;
}

const { t } = useI18n();

// 响应式数据
const dialogVisible = ref(false);
const searchKeyword = ref("");
const fileTreeRef = ref();
const selectedItems = ref<FileItem[]>([]);
const fileTreeData = ref<TreeNode[]>([]);
const loading = ref(false);
const errorMessage = ref("");
const isInitialized = ref(false); // 添加初始化标记
const isOpening = ref(false); // 添加打开状态标记

const quickFolders = ref<any[]>([]);
const activeQuickFolderPath = ref<string>("");

watch(quickFolders, val => {
  console.log("quickFolders:", val);
});

// 在 <script setup> 区域添加 handleGoRoot 方法
const handleGoRoot = () => {
  loadRootDirectories();
  activeQuickFolderPath.value = "";
};

// 快捷方式图标映射
const folderIconMap: Record<string, any> = {
  desktop: Monitor,
  documents: Document,
  downloads: Download,
  music: Headset,
  pictures: Picture,
  videos: VideoCamera
};
const getFolderIcon = (key: string) => {
  return folderIconMap[key?.toLowerCase?.()] || Folder;
};

// 树形组件配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf"
};

// 打开对话框
const open = async () => {
  // 防止重复打开
  if (isOpening.value) {
    return;
  }

  isOpening.value = true;
  dialogVisible.value = true;
  errorMessage.value = "";

  // 清除之前的选中状态
  selectedItems.value = [];
  lastCheckedNode = null;
  if (fileTreeRef.value) {
    fileTreeRef.value.setCheckedKeys([]);
  }

  try {
    // 如果已经初始化过，直接显示
    if (isInitialized.value) {
      return;
    }

    // 显示加载状态
    loading.value = true;

    // 并行加载，但不阻塞对话框显示
    const loadPromises = [
      loadQuickFolders().catch(err => {
        console.warn("加载快捷文件夹失败:", err);
      }),
      loadRootDirectories().catch(err => {
        console.warn("加载根目录失败:", err);
      })
    ];

    // 使用 Promise.allSettled 确保即使部分失败也能继续
    await Promise.allSettled(loadPromises);

    isInitialized.value = true;
  } catch (error) {
    console.error("打开文件选择器失败:", error);
    errorMessage.value = t("common.customFileSelector.error.loadFailed");
  } finally {
    loading.value = false;
    isOpening.value = false;
  }
};

// 加载系统常用文件夹（带重试机制）
const loadQuickFolders = async (retryCount = 0) => {
  const maxRetries = 2;

  try {
    // 添加超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("加载快捷文件夹超时")), 5000);
    });

    const apiPromise = osControlApi.getSystemSpecialFolders();
    const res: any = await Promise.race([apiPromise, timeoutPromise]);

    // 兼容 res 直接为数组或为 { data: [...] }
    if (Array.isArray(res)) {
      quickFolders.value = res as SystemFolder[];
    } else if (Array.isArray(res.data)) {
      quickFolders.value = res.data as SystemFolder[];
    } else {
      quickFolders.value = [];
    }
    console.log("getSystemSpecialFolders 返回：", res);
  } catch (error) {
    console.error(`Failed to load quick folders (attempt ${retryCount + 1}):`, error);

    if (retryCount < maxRetries) {
      // 重试
      console.log(`Retrying to load quick folders... (${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
      return loadQuickFolders(retryCount + 1);
    } else {
      // 最终失败，使用默认值
      console.warn("Using default quick folders after retries failed");
      quickFolders.value = [
        { name: "桌面", path: "C:\\Users\\<USER>\\Desktop", key: "desktop" },
        { name: "文档", path: "C:\\Users\\<USER>\\Documents", key: "documents" },
        { name: "下载", path: "C:\\Users\\<USER>\\Downloads", key: "downloads" }
      ];
    }
  }
};

// 快捷跳转到常用文件夹
const jumpToQuickFolder = async (folder: any) => {
  loading.value = true;
  errorMessage.value = "";
  activeQuickFolderPath.value = folder.path;
  try {
    const children = await getDirectoryContents(folder.path);
    fileTreeData.value = children;
  } catch (e) {
    errorMessage.value = t("common.customFileSelector.error.loadFailed");
  } finally {
    loading.value = false;
  }
};

// 加载根目录（带重试机制）
const loadRootDirectories = async (retryCount = 0) => {
  const maxRetries = 2;

  try {
    // 添加超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("加载根目录超时")), 8000);
    });

    const apiPromise = osControlApi.getRootDirectories();
    const rootDirs: any = await Promise.race([apiPromise, timeoutPromise]);

    fileTreeData.value = (rootDirs.data || rootDirs) as TreeNode[];
    errorMessage.value = ""; // 清除错误信息
  } catch (error) {
    console.error(`Failed to load root directories (attempt ${retryCount + 1}):`, error);

    if (retryCount < maxRetries) {
      // 重试
      console.log(`Retrying to load root directories... (${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
      return loadRootDirectories(retryCount + 1);
    } else {
      // 最终失败，显示错误并使用默认值
      errorMessage.value = t("common.customFileSelector.error.loadFailed") + " (已重试" + maxRetries + "次)";
      fileTreeData.value = [
        { path: "C:\\", name: "C:", isDirectory: true, leaf: false },
        { path: "D:\\", name: "D:", isDirectory: true, leaf: false },
        { path: "E:\\", name: "E:", isDirectory: true, leaf: false }
      ] as TreeNode[];
    }
  }
};

// 加载子节点
const loadNode = async (node: any, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    resolve(fileTreeData.value);
    return;
  }

  try {
    const path = node.data.path;
    const children = await getDirectoryContents(path);
    resolve(children);
  } catch (error) {
    console.error("Failed to load node:", error);
    // 返回空数组而不是抛出错误，让树形组件继续工作
    resolve([]);
  }
};

// 目录内容缓存
const directoryCache = new Map<string, { data: TreeNode[]; timestamp: number }>();
const CACHE_DURATION = 30000; // 30秒缓存

// 获取目录内容（带重试和缓存机制）
const getDirectoryContents = async (dirPath: string, retryCount = 0): Promise<TreeNode[]> => {
  const maxRetries = 2;

  try {
    // 检查缓存
    const cached = directoryCache.get(dirPath);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }

    // 添加超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("获取目录内容超时")), 10000);
    });

    const apiPromise = osControlApi.getDirectoryContents({ path: dirPath });
    const contents: any = await Promise.race([apiPromise, timeoutPromise]);

    const data = contents.data || contents;
    const result = data.map((item: any) => ({
      path: item.path,
      name: item.name,
      isDirectory: item.isDirectory,
      leaf: item.leaf,
      modifiedAt: item.modifiedAt ? new Date(item.modifiedAt) : undefined,
      createdAt: item.createdAt ? new Date(item.createdAt) : undefined,
      accessedAt: item.accessedAt ? new Date(item.accessedAt) : undefined,
      size: item.size || 0
    }));

    // 缓存结果
    directoryCache.set(dirPath, { data: result, timestamp: Date.now() });

    return result;
  } catch (error) {
    console.error(`Failed to get directory contents for ${dirPath} (attempt ${retryCount + 1}):`, error);

    if (retryCount < maxRetries) {
      // 重试
      console.log(`Retrying to get directory contents for ${dirPath}... (${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
      return getDirectoryContents(dirPath, retryCount + 1);
    } else {
      // 最终失败，返回空数组
      console.warn(`Failed to load directory contents for ${dirPath} after ${maxRetries} retries`);
      return [];
    }
  }
};

// 节点点击事件只保留 lastSelectedNode 记录
const handleNodeClick = () => {
  console.log("handleNodeClick");
};

// 节点选择事件
let lastCheckedNode: TreeNode | null = null;
let isShiftPressed = false;

// 监听键盘事件来跟踪 Shift 键状态
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === "Shift") {
    isShiftPressed = true;
  }
};

const handleKeyUp = (event: KeyboardEvent) => {
  if (event.key === "Shift") {
    isShiftPressed = false;
  }
};

// 在组件挂载时添加事件监听器
const addKeyboardListeners = () => {
  document.addEventListener("keydown", handleKeyDown);
  document.addEventListener("keyup", handleKeyUp);
};

// 在组件卸载时移除事件监听器
const removeKeyboardListeners = () => {
  document.removeEventListener("keydown", handleKeyDown);
  document.removeEventListener("keyup", handleKeyUp);
};

// 手动重试加载
const retryLoad = async () => {
  loading.value = true;
  errorMessage.value = "";

  try {
    // 重新加载数据
    const loadPromises = [
      loadQuickFolders().catch(err => {
        console.warn("重试加载快捷文件夹失败:", err);
      }),
      loadRootDirectories().catch(err => {
        console.warn("重试加载根目录失败:", err);
      })
    ];

    await Promise.allSettled(loadPromises);
  } catch (error) {
    console.error("重试加载失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleNodeCheck = async (data: TreeNode, checkedInfo: any) => {
  // 支持 Shift 区间智能切换（同一级目录下）
  if (isShiftPressed && lastCheckedNode && fileTreeRef.value) {
    const node = fileTreeRef.value.getNode(data.path);
    const lastNode = fileTreeRef.value.getNode(lastCheckedNode.path);
    if (node && lastNode && node.parent === lastNode.parent) {
      const siblings = node.parent.childNodes.map((n: any) => n.data);
      const startIdx = siblings.findIndex((item: any) => item.path === lastCheckedNode!.path);
      const endIdx = siblings.findIndex((item: any) => item.path === data.path);
      if (startIdx !== -1 && endIdx !== -1) {
        const [from, to] = [startIdx, endIdx].sort((a, b) => a - b);
        const range = siblings.slice(from, to + 1);

        // 获取当前选中的keys
        const checkedKeys = fileTreeRef.value.getCheckedKeys();

        // 统计区间内已选中的文件数量
        const checkedInRange = range.filter((item: any) => checkedKeys.includes(item.path)).length;
        const totalInRange = range.length;

        // 智能判断：如果超过一半已选中，则全部取消；否则全部选中
        const shouldSelectAll = checkedInRange <= totalInRange / 2;

        if (shouldSelectAll) {
          // 区间内文件大部分未选中 → 全部选中
          range.forEach((item: any) => {
            fileTreeRef.value.setChecked(item.path, true);
          });
          console.log(`Shift+点击区间选择: ${range.length} 个文件全部选中`);
        } else {
          // 区间内文件大部分已选中 → 全部取消
          range.forEach((item: any) => {
            fileTreeRef.value.setChecked(item.path, false);
          });
          console.log(`Shift+点击区间取消: ${range.length} 个文件全部取消`);
        }
      }
    } else {
      if (typeof window !== "undefined") {
        window.alert("仅支持同一目录下的区间选择，请先展开需要选择的目录");
      }
    }
  }
  lastCheckedNode = data;

  // 获取所有已勾选节点，包含区间内新勾选的
  let checkedNodes: TreeNode[] = [];
  if (fileTreeRef.value) {
    checkedNodes = fileTreeRef.value.getCheckedNodes();
  } else {
    checkedNodes = checkedInfo.checkedNodes;
  }

  // 去重处理
  const uniqueNodes = Array.from(new Map(checkedNodes.map(node => [node.path, node])).values());

  // 获取 size 信息并保留时间信息 - 使用快速模式避免卡顿
  const sizePromises = uniqueNodes.map(async (node: TreeNode) => {
    try {
      // 对于文件夹使用快速模式，对于文件使用原有逻辑
      if (node.isDirectory) {
        const res = await osControlApi.getFileOrFolderSizeAsync({
          path: node.path,
          mode: "fast", // 快速模式：只统计直接子文件
          timeout: 5000 // 5秒超时
        });

        if (res.error) {
          console.warn(`获取文件夹大小失败: ${node.path}`, res.error);
        }

        return {
          path: node.path,
          name: node.name,
          isDirectory: node.isDirectory,
          size: res.size || 0,
          modifiedAt: node.modifiedAt || new Date(),
          sizeNote: res.error ? "计算失败" : res.size > 0 ? "仅直接子文件" : "空文件夹"
        };
      } else {
        // 文件直接使用已有的大小信息
        return {
          path: node.path,
          name: node.name,
          isDirectory: node.isDirectory,
          size: node.size || 0,
          modifiedAt: node.modifiedAt || new Date()
        };
      }
    } catch (error) {
      console.warn(`获取文件大小失败: ${node.path}`, error);
      return {
        path: node.path,
        name: node.name,
        isDirectory: node.isDirectory,
        size: 0,
        modifiedAt: node.modifiedAt || new Date(),
        sizeNote: "计算失败"
      };
    }
  });

  selectedItems.value = await Promise.all(sizePromises);
};

// 搜索功能
const handleSearch = () => {
  if (fileTreeRef.value) {
    fileTreeRef.value.filter(searchKeyword.value);
  }
};

// 过滤节点
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 移除选中的项目
const removeSelectedItem = (item: FileItem) => {
  const index = selectedItems.value.findIndex(i => i.path === item.path);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
    // 同时取消树形组件中的选中状态
    if (fileTreeRef.value) {
      fileTreeRef.value.setChecked(item.path, false);
    }
  }
};

// 清空所有选中项目
const clearSelected = () => {
  selectedItems.value = [];
  if (fileTreeRef.value) {
    fileTreeRef.value.setCheckedKeys([]);
  }
};

// 确认选择
const handleConfirm = () => {
  const result = selectedItems.value.length === 1 ? selectedItems.value[0] : selectedItems.value;
  emit("confirm", result);
  handleClose();
};

// 取消选择
const handleCancel = () => {
  emit("cancel");
  handleClose();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  selectedItems.value = [];
  searchKeyword.value = "";
  emit("close");
};

// 事件定义
const emit = defineEmits<{
  confirm: [items: FileItem | FileItem[]];
  cancel: [];
  close: [];
}>();

// 组件生命周期
onMounted(() => {
  addKeyboardListeners();
});

onUnmounted(() => {
  removeKeyboardListeners();
});

// 暴露方法
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.custom-file-selector {
  .dialog-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    .dialog-title-icon {
      margin-right: 8px;
      font-size: 22px;
      color: #409eff;
    }
    .dialog-title-text {
      flex: 1;
    }
  }
  .selector-container {
    display: flex;
    gap: 24px;
    height: 420px;
    padding: 16px 12px;
    background: #f8fafd;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  }
  .file-tree-container {
    display: flex;
    flex-basis: 32%;
    flex-direction: column;
    min-width: 220px;
    max-width: 320px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgb(64 158 255 / 4%);
    .quick-folders {
      padding: 8px 12px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
    }
    .tree-header {
      padding: 10px 12px 6px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
    }
    .tree-content {
      flex: 1;
      padding: 10px 12px;
      overflow-x: auto;
      overflow-y: auto;
      // 关键：让 el-tree 及其内容宽度自适应
      .el-tree {
        min-width: max-content;
        width: auto !important;
      }
      .el-tree-node__content {
        min-width: max-content;
        width: auto !important;
      }
      .loading-state,
      .error-state {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #909399;
        .is-loading {
          animation: rotating 2s linear infinite;
        }
      }
      .error-state {
        color: #f56c6c;
      }
    }
  }
  .selected-items-container {
    display: flex;
    flex-basis: 68%;
    flex-direction: column;
    min-width: 320px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgb(64 158 255 / 4%);
    .selected-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .selected-content {
      flex: 1;
      padding: 12px 16px;
      overflow: auto;
      .selected-item {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 10px 8px;
        margin-bottom: 10px;
        background: #f9fafc;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        transition: box-shadow 0.2s;
        &:hover {
          box-shadow: 0 2px 8px rgb(64 158 255 / 8%);
        }
        .item-name {
          flex: 1;
          font-weight: 500;
        }
        .item-path {
          flex: 2;
          overflow: hidden;
          font-size: 12px;
          color: #909399;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-size {
          flex: 1;
          font-size: 12px;
          color: #67c23a;
          text-align: right;
        }
      }
      .empty-state {
        padding: 40px 0;
        color: #909399;
        text-align: center;
      }
    }
  }
  .tree-node {
    display: flex;
    gap: 8px;
    align-items: center;
    .folder-icon {
      color: #e6a23c;
    }
    .file-icon {
      color: #409eff;
    }
    .node-label {
      flex: 1;
    }
  }
  .dialog-footer {
    padding: 8px 0 0;
    text-align: right;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
