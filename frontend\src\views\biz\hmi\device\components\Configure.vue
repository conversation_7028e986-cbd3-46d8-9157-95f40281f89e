<template>
  <div class="configure-container">
    <div
      v-if="!hmiInfo.currConfigure || hmiInfo.currConfigure.type != 'hmi'"
      class="configure-empty flx-start"
      :style="configureMainStyle.mainRoot"
    >
      <img src="@/assets/images/configure_not_found.png" class="not-img" />
      <div class="not-detail">
        <h4>{{ t("hmi.device.configure.selectConfigure") }}</h4>
      </div>
    </div>
    <div v-else class="configure-main" :style="configureMainStyle.mainRoot">
      <GraphView
        ref="graphView"
        @render-data="onRenderData"
        @trigger-click="onGraphClick"
        v-loading="form.graphViewLoading"
        :element-loading-text="form.loadingText"
        element-loading-background="rgba(255,255,255,0.5)"
      ></GraphView>
    </div>
  </div>
  <el-dialog
    v-model="form.remoteSetShow"
    :draggable="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="t('device.configure.remoteSet')"
    width="30%"
  >
    <RemoteSet :device-id="prop.deviceId" :equipment-config="form.equipmentDataConfig" @end="onRemoSetEnd"></RemoteSet>
  </el-dialog>
</template>

<script setup lang="ts">
import mittBus from "@/utils/mittBus";
import { GraphView } from "@/views/biz/hmi/packages";
import {
  EquipmentConfig,
  EventTypeParams,
  getCellDataEquipmentConfig,
  GraphViewData,
  RenderData,
  SelectControlType,
  SelectControlValue,
  SetSaddrValue
} from "@/views/biz/hmi/packages/graph";
import { useGlobalStore, useHmiStore } from "@/stores/modules";
import { storeToRefs } from "pinia";
import { Configure, GraphGetDataRequest, GraphRegisterResponse, GraphRemoteControlRequest } from "@/api/interface/biz/hmi";
import { configureInfoApi, graphDefineApi, graphViewApi } from "@/api/modules/biz/hmi";
import { ResultData } from "@/api";
import Message from "@/scripts/message";
import { CtlMode } from "@/api/interface/biz/debug/remote";
import RemoteSet from "./RemoteSet.vue";
import { DebugDeviceInfo } from "@/stores/interface";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const prop = defineProps<{
  deviceId: string;
}>();
const emit = defineEmits<{
  (e: "end"): void;
}>();
const globalStore = useGlobalStore();
const hmiStore = useHmiStore();
const { hmiInfo } = storeToRefs(hmiStore);
const graphView = ref();
let timerDispose = false;
const registerIdList: string[] = [];
const form = ref({
  graphViewLoading: false,
  loadingText: "",
  remoteSetShow: false,
  equipmentDataConfig: {}
});

const configureMainStyle = computed<any>(() => {
  let offset = 98;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return { mainRoot: { height: `calc(100vh  - ${offset}px)` } };
});
onMounted(() => {
  loadData();
});
onUnmounted(() => {
  timerDispose = true;
});
// 监视组态
watch(hmiStore.hmiInfo, () => {
  if (hmiStore.hmiInfo.currConfigure && hmiStore.hmiInfo.currConfigure.type == "hmi") {
    console.log(t("hmi.device.configure.symbolChangeReload"), prop.deviceId, hmiStore.hmiInfo.currConfigure);
    loadData();
  }
});
// 监视设备是否变动
watch(prop, () => {
  console.log(t("hmi.device.configure.deviceChangeReload"), prop.deviceId);
  loadData();
});
// 监视设备列表连接信息
mittBus.on("deviceConnectSuccess", async req => {
  const data = req as DebugDeviceInfo;
  if (data.id == prop.deviceId) {
    console.log(t("hmi.device.configure.deviceConnectReload"), prop.deviceId);
    loadData();
  }
});
const loadData = async () => {
  // 清空缓存
  while (registerIdList.length > 0) {
    registerIdList.pop();
  }
  form.value.graphViewLoading = true;
  form.value.loadingText = t("hmi.device.configure.loading");
  try {
    await registerData();
  } finally {
    setTimeout(() => {
      form.value.graphViewLoading = false;
    }, 0);
  }
};

const registerData = async () => {
  if (!hmiStore.hmiInfo || !hmiStore.hmiInfo.currConfigure || hmiStore.hmiInfo.currConfigure.type != "hmi") {
    return;
  }
  const project: Configure.LoadConfigureInfo = {
    id: hmiStore.hmiInfo.currConfigure.id,
    path: hmiStore.hmiInfo.currConfigure.path!,
    label: hmiStore.hmiInfo.currConfigure.label!
  };
  const res: ResultData = await configureInfoApi.loadConfigure(project);
  if (res.code != 0) {
    Message.error(t("hmi.device.configure.loadFailed") + res.msg);
    return;
  }
  const equipmentResult = await graphDefineApi.get();
  if (equipmentResult.code != 0) {
    ElMessageBox.alert(t("hmi.device.configure.getCustomDeviceFailed"), {
      title: t("hmi.device.configure.remoteSet"),
      type: "error"
    });
    return;
  }
  const graphViewData: GraphViewData = {
    graphData: res.data,
    equipmentDatas: [],
    cbrDis: {
      open: "1",
      close: "0"
    }
  };
  if (equipmentResult.data) {
    graphViewData.equipmentDatas = equipmentResult.data;
  }
  if (graphView && graphView.value) {
    graphView.value.setGraphData(graphViewData);
  }
};
const onRenderData = async (renderData: RenderData) => {
  // 注册短地址
  const saddrs: string[] = [];
  if (renderData.yxMap.size > 0) {
    for (const item of renderData.yxMap) {
      saddrs.push(item[0]);
    }
  }
  if (saddrs.length == 0) {
    return;
  }
  const result: ResultData<GraphRegisterResponse> = await graphViewApi.register({
    deviceId: prop.deviceId,
    hmiId: hmiInfo.value.currConfigure.id,
    saddrs: saddrs
  });
  if (result.code != 0) {
    Message.error(t("hmi.device.configure.registerDataFailed") + result.msg);
    return;
  }
  // 判断使用存在非法变量
  if (result.data.notFoundSaddrs.length > 0) {
    const arrs: string[] = [];
    for (const item of result.data.notFoundSaddrs) {
      arrs.push(item);
    }
    Message.error(t("hmi.device.configure.variablesNotExist") + arrs.join(","));
    // 如果全部变量都不存在，则不需要读取
    if (result.data.notFoundSaddrs.length == saddrs.length) {
      return;
    }
  }
  // 定时查询数据
  registerIdList.push(result.data.registerId);
  getRegisterValueLoop(result.data.registerId);
};
const getRegisterValueLoop = async (registerId: string) => {
  const ret = await getRegisterValue(registerId);
  if (!ret) {
    return;
  }
  setTimeout(() => {
    // 检查循环条件
    if (!timerDispose && registerIdList.includes(registerId)) {
      // 循环调用
      getRegisterValueLoop(registerId);
    }
  }, 2000);
};
const getRegisterValue = async (registerId: string): Promise<boolean> => {
  const data: GraphGetDataRequest = {
    deviceId: prop.deviceId,
    registerId: registerId
  };
  const result: ResultData = await graphViewApi.getData(data);
  if (result.code != 0) {
    Message.error(t("hmi.device.configure.getDataError") + result.msg);
    return false;
  }
  registersAddr(result.data);
  return true;
};
const registersAddr = (data: SetSaddrValue[]) => {
  // 注册成功后，启动定时器定时读取数据，并设置值
  setSaddrValue(data);
};
const setSaddrValue = (data: SetSaddrValue[]) => {
  if (graphView && graphView.value) {
    graphView.value.setSaddrValue(data);
  }
};

const onGraphClick = (args: EventTypeParams) => {
  if (args.type != "node:click") {
    return;
  }
  const node = args.eventParam.node;
  if (!node) {
    return;
  }
  const equipmentDataConfig = getCellDataEquipmentConfig(node);
  if (!equipmentDataConfig) {
    return;
  }
  if (equipmentDataConfig.controlSAddr && equipmentDataConfig.controlSAddr.trim().length > 0) {
    onRemoteControl(equipmentDataConfig);
  } else if (equipmentDataConfig.setSAddr && equipmentDataConfig.setSAddr.trim().length > 0) {
    onRemoteSet(equipmentDataConfig);
  }
};
const onRemoteControl = async (equipmentDataConfig: EquipmentConfig) => {
  form.value.graphViewLoading = true;
  form.value.loadingText = t("hmi.device.configure.operating");
  try {
    await remoteControl(equipmentDataConfig);
  } finally {
    if (graphView && graphView.value) {
      graphView.value.unselect();
    }
    form.value.graphViewLoading = false;
  }
};

const remoteControl = async (equipmentDataConfig: EquipmentConfig) => {
  const requestData: GraphRemoteControlRequest = {
    deviceId: prop.deviceId,
    data: {
      ctlObj: equipmentDataConfig.controlSAddr,
      ctlModel: equipmentDataConfig.controlType == SelectControlType.DIRECT ? CtlMode.DIRECT : CtlMode.SBO,
      ctlVal: equipmentDataConfig.controlValue == SelectControlValue.SWITCH_OPEN ? "1" : "2",
      check: "no_check",
      test: "0",
      opemTm: new Date().toISOString()
    }
  };
  // 判断是直控还是选控
  if (
    equipmentDataConfig.controlType == SelectControlType.DIRECT ||
    equipmentDataConfig.controlType == SelectControlType.SELECT
  ) {
    const result: ResultData = await graphViewApi.remoteControl(requestData);
    if (result.code != 0) {
      Message.info(t("hmi.device.configure.remoteControlFailed") + result.msg);
      return;
    }
    Message.success(t("hmi.device.configure.remoteControlSuccess"));
    emit("end");
  } else {
    Message.warning(t("hmi.device.configure.noRemoteControlType"));
  }
};

const onRemoteSet = (equipmentDataConfig: EquipmentConfig) => {
  form.value.remoteSetShow = true;
  form.value.equipmentDataConfig = equipmentDataConfig;
  if (graphView && graphView.value) {
    graphView.value.unselect();
  }
};
const onRemoSetEnd = () => {
  form.value.remoteSetShow = false;
};
</script>

<style scoped lang="scss">
.configure-container {
  flex-direction: row;
  width: 100%;
  .configure-main {
    width: 100%;
  }
  .configure-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: auto;
    height: 100%;
    color: var(--el-text-color-secondary);
    background: var(--el-bg-color);
    .not-img {
      width: 300px;
      height: 300px;
      margin-right: 50px;
    }
  }
}
</style>
