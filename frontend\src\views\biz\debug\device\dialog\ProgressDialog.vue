<template>
  <el-dialog
    v-model="progressDialog.show"
    width="50%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('device.progress.title')"
  >
    <div style="min-height: 120px; padding: 15px">
      <el-progress
        :duration="3"
        :percentage="progressDialog.percentage"
        :text-inside="false"
        striped
        :stroke-width="10"
        :indeterminate="progressDialog.indeterminate"
        style="margin-top: 20px"
      >
        <span>{{ displayProgressText }}</span>
      </el-progress>

      <!-- 详细进度信息 -->
      <div v-if="progressDialog.showDetails" class="progress-details">
        <div class="progress-item">
          <span>{{ t("device.progress.currentFile") }}: {{ progressDialog.currentFile || "--" }}</span>
        </div>
        <div class="progress-item">
          <span>{{ t("device.progress.fileProgress") }}: {{ progressDialog.completedFiles }}/{{ progressDialog.totalFiles }}</span>
        </div>
        <div v-if="progressDialog.currentFile && progressDialog.currentFileProgress > 0" class="progress-item">
          <span>{{ t("device.progress.currentFileProgress") }}: {{ progressDialog.currentFileProgress.toFixed(1) }}%</span>
        </div>
      </div>

      <!-- 取消按钮 -->
      <div v-if="progressDialog.showCancel" style="text-align: center; margin-top: 20px">
        <el-button type="danger" @click="handleCancel">
          {{ t("device.progress.cancel") }}
        </el-button>
      </div>
    </div>
    <template #header>
      <div class="dialog-header">
        <el-icon><ChromeFilled /></el-icon>
        <span>{{ t("device.progress.title") }}</span>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ChromeFilled } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { computed, ref } from "vue";

const { t } = useI18n();

// 使用响应式的默认进度文本
const defaultProgressText = computed(() => t("device.progress.executing"));

const progressDialog = ref({
  show: false,
  percentage: 0,
  progressText: "", // 空字符串表示使用默认文本
  indeterminate: false, // 默认显示确定的进度条
  showCancel: false,
  showDetails: false,
  currentFile: "",
  completedFiles: 0,
  totalFiles: 0,
  currentFileProgress: 0
});

// 计算显示的进度文本，如果没有自定义文本则使用默认的响应式文本
const displayProgressText = computed(() => {
  return progressDialog.value.progressText || defaultProgressText.value;
});
const show = (): void => {
  progressDialog.value.show = true;
};
const hide = (): void => {
  progressDialog.value.show = false;
};
const setProgress = (percentage?: number, progressText?: string, indeterminate?: boolean): void => {
  if (typeof percentage === "number") progressDialog.value.percentage = percentage;
  if (typeof progressText === "string") progressDialog.value.progressText = progressText;
  if (typeof indeterminate === "boolean") progressDialog.value.indeterminate = indeterminate;
};

const setDetails = (currentFile: string, currentFileIndex: number, totalFiles: number, currentFileProgress?: number): void => {
  progressDialog.value.currentFile = currentFile;
  progressDialog.value.completedFiles = currentFileIndex; // 这里存储的是当前显示的文件序号（用于显示 X/Y）
  progressDialog.value.totalFiles = totalFiles;
  if (typeof currentFileProgress === "number") {
    progressDialog.value.currentFileProgress = currentFileProgress;
  } else {
    progressDialog.value.currentFileProgress = 0; // 重置当前文件进度
  }
  progressDialog.value.showDetails = true;
  console.log(`[ProgressDialog] setDetails - 文件: ${currentFile}, 显示: ${currentFileIndex}/${totalFiles}, 文件进度: ${currentFileProgress || 0}%`);
};

const setCancel = (showCancel: boolean): void => {
  progressDialog.value.showCancel = showCancel;
};

// 定义取消事件
const emit = defineEmits<{
  cancel: [];
}>();

const handleCancel = (): void => {
  emit("cancel");
};

defineExpose({
  show,
  hide,
  setProgress,
  setDetails,
  setCancel,
  progressDialog
});
</script>
<style lang="css" scoped>
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}

.progress-details {
  margin-top: 20px;
  padding: 10px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  font-size: 14px;
}

.progress-item {
  margin-bottom: 8px;
  color: var(--el-text-color-regular);
}

.progress-item:last-child {
  margin-bottom: 0;
}
</style>
