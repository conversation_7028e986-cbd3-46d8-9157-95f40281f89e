{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "types": ["vite/client", "element-plus/global"],

    /* Strict Type-Checking Options */
    "strict": true /* Enable all strict type-checking options. */,
    "noImplicitAny": false /* Raise error on expressions and declarations with an implied 'any' type. */,
    "noImplicitThis": false /* Raise error on 'this' expressions with an implied 'any' type. */,

    /* 性能优化选项 */
    "jsx": "preserve",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ],
    "skipLibCheck": true,
    "noEmit": true,
/* 路径映射 */
    "baseUrl": "./",
    "paths": {
      "@": ["src"],
      "@/*": ["src/*"]
    },
    /* 增量编译 */
    "incremental": true,
    "tsBuildInfoFile": "node_modules/.cache/typescript/tsbuildinfo"
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/types/**/*.d.ts",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "vite.config.ts",
    "components.d.ts",
    "auto-imports.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js",
    "public",
    "scripts"
  ]
}
