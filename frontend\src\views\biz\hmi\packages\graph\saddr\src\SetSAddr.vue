<!-- eslint-disable vue/no-mutating-props -->
<template>
  <el-form>
    <el-row>
      <el-col :span="12">
        <el-form-item :label="t('hmi.graph.setSAddr.telemetry')" label-width="80">
          <el-input v-model="equipmentConfig.saddr"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('hmi.graph.setSAddr.format')" label-width="80">
          <el-input v-model="equipmentConfig.format"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('hmi.graph.setSAddr.factor')" label-width="80">
          <el-input-number v-model="equipmentConfig.factor"></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="t('hmi.graph.setSAddr.remoteControl')" label-width="80">
          <el-input v-model="equipmentConfig.controlSAddr"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('hmi.graph.setSAddr.controlType')" label-width="80">
          <el-select v-model="equipmentConfig.controlType">
            <el-option
              v-for="item in selectControlTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('hmi.graph.setSAddr.controlValue')" label-width="80">
          <el-select v-model="equipmentConfig.controlValue">
            <el-option
              v-for="item in selectControlValueList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="t('hmi.graph.setSAddr.remoteSet')" label-width="80">
          <el-input v-model="equipmentConfig.setSAddr"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('hmi.graph.setSAddr.setType')" label-width="80">
          <el-select v-model="equipmentConfig.setType">
            <el-option
              v-for="item in selectControlTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-tabs>
      <el-tab-pane :label="t('hmi.graph.setSAddr.displayConfig')">
        <div style="padding: 0 0 15px; text-align: left">
          <el-button type="success" class="setSaddr-btn" @click="addRow">{{ t("hmi.graph.setSAddr.addRow") }}</el-button>
        </div>
        <el-table :max-height="200" :data="equipmentConfig.showCfg" :border="true" :stripe="true" @cell-dblclick="onCellDbClick">
          <el-table-column :label="t('hmi.graph.setSAddr.sequence')" type="index" width="60"></el-table-column>
          <el-table-column :label="t('hmi.graph.setSAddr.type')" width="100" align="center" prop="type">
            <template #default="scope">
              <el-select v-model="scope.row.type">
                <el-option v-for="item in showValueType" :label="item.label" :value="item.value" :key="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="t('hmi.graph.setSAddr.originalValue')" width="100" align="center" prop="value">
            <template #default="scope">
              <el-input v-if="scope.row.edit == 'value'" v-model="scope.row.value" @blur="onBlur"></el-input>
              <span v-else>{{ scope.row.value }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('hmi.graph.setSAddr.displayValue')" align="center">
            <template #default="scope">
              <div v-if="scope.row.type == EquipmentShowType.EQUIPMENT">
                <el-space>
                  <el-image :src="item.img" v-for="(item, index) in scope.row.showValue.components" :key="index" class="s-img">
                    <template #error>
                      <el-icon :size="20">
                        <Picture></Picture>
                      </el-icon>
                    </template>
                  </el-image>
                </el-space>
              </div>
              <div v-else>
                <el-input v-if="scope.row.edit == 'showValue'" v-model="scope.row.showValue" @blur="onBlur"></el-input>
                <span v-else>{{ scope.row.showValue }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="t('hmi.graph.setSAddr.operation')" align="center">
            <template #default="scope">
              <el-space>
                <el-icon :size="20" class="equipment-icon" @click="onDelete(scope.$index)">
                  <Delete></Delete>
                </el-icon>
              </el-space>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <div class="setSaddr-btn-right">
      <el-button type="success" class="setSaddr-btn" @click="onConfirm">{{ t("hmi.graph.setSAddr.confirm") }}</el-button>
      <el-button type="default" class="setSaddr-btn" @click="onCancel">{{ t("hmi.graph.setSAddr.cancel") }}</el-button>
    </div>
  </el-form>

  <el-dialog
    v-model="prop.value.equipmentShow"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="t('hmi.graph.setSAddr.selectSymbol')"
    width="40%"
  >
    <SelectEquipment :data="prop.value.equipmentList" @end="onSelectedEquipment"></SelectEquipment>
  </el-dialog>
</template>
<script setup lang="ts">
import { Delete, Picture } from "@element-plus/icons-vue";
import { Cell } from "@antv/x6";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import SelectEquipment from "../../../graph/SelectEquipment.vue";
import {
  EquipmentConfig,
  EquipmentData,
  EquipmentShowConfig,
  EquipmentShowType,
  Select,
  SelectControlType,
  SelectControlValue
} from "../../../graph";
import { ElMessageBox } from "element-plus";

const { t } = useI18n();

interface SetSAddrData {
  cell: unknown;
  equipmentShow: boolean;
  equipmentList: unknown[];
}
// 定义属性
const prop = defineProps<{
  value: SetSAddrData;
}>();
// 定义事件
const emit = defineEmits<{
  (e: "getEquipmentList"): void;
  (e: "end", data?: EquipmentConfig): void;
}>();
const form = ref<{
  saddrShow: boolean;
  rowData: unknown;
}>({
  saddrShow: true,
  rowData: undefined
});
const selectControlTypeList: Select<string>[] = [
  { label: t("hmi.graph.setSAddr.selectControl"), value: SelectControlType.SELECT },
  { label: t("hmi.graph.setSAddr.directControl"), value: SelectControlType.DIRECT }
];
const selectControlValueList: Select<number>[] = [
  { label: t("hmi.graph.setSAddr.controlClose"), value: SelectControlValue.SWITCH_CLOSE },
  { label: t("hmi.graph.setSAddr.controlOpen"), value: SelectControlValue.SWITCH_OPEN }
];
const showValueType: Select<EquipmentShowType>[] = [
  { label: t("hmi.graph.setSAddr.text"), value: EquipmentShowType.TEXT },
  { label: t("hmi.graph.setSAddr.symbol"), value: EquipmentShowType.EQUIPMENT }
];

const equipmentConfig = ref<EquipmentConfig>({
  saddr: "",
  format: "",
  factor: 1,
  controlSAddr: "",
  controlType: SelectControlType.SELECT,
  controlValue: SelectControlValue.SWITCH_CLOSE,
  setSAddr: "",
  setType: SelectControlType.SELECT,
  showCfg: []
});
const initData = () => {
  const cellData = (prop.value.cell as Cell).getData();
  if (!cellData) {
    return;
  }
  const data = cellData.equipmentConfig;
  if (!data) {
    return;
  }
  equipmentConfig.value = data;
  if (equipmentConfig.value.controlValue == SelectControlValue.NONE) {
    equipmentConfig.value.controlValue = SelectControlValue.SWITCH_CLOSE;
  }
};
initData();
const addRow = () => {
  const row: EquipmentShowConfig = {
    value: "",
    type: EquipmentShowType.TEXT,
    showValue: "",
    edit: "none"
  };
  equipmentConfig.value.showCfg.push(row);
};
const deleteRow = (index: number) => {
  equipmentConfig.value.showCfg.splice(index, 1);
};

const onDelete = (index: number) => {
  ElMessageBox.confirm(t("hmi.graph.setSAddr.confirmDelete"), {
    title: t("hmi.graph.setSAddr.tip"),
    type: "warning"
  })
    .then(() => {
      deleteRow(index);
    })
    .catch(() => {
      console.log(t("hmi.graph.setSAddr.cancelDelete"));
    });
};
const onCellDbClick = (row: EquipmentShowConfig, col: any) => {
  const colIndex = col.no;
  if (colIndex == 2) {
    row.edit = "value";
  } else if (colIndex == 3) {
    if (row.type == EquipmentShowType.EQUIPMENT) {
      // 弹出设备选择界面
      emit("getEquipmentList");
    } else {
      row.edit = "showValue";
    }
  }
  form.value.rowData = row;
};
const onBlur = () => {
  if (form.value.rowData) {
    (form.value.rowData as EquipmentShowConfig).edit = "none";
  }
};
const onSelectedEquipment = (data: EquipmentData) => {
  // eslint-disable-next-line vue/no-mutating-props
  prop.value.equipmentShow = false;
  if (form.value.rowData) {
    const rowData = form.value.rowData as EquipmentShowConfig;
    rowData.showValue = data;
  }
};
const onConfirm = () => {
  emit("end", equipmentConfig.value as EquipmentConfig);
};
const onCancel = () => {
  emit("end");
};
</script>
<style>
.setSaddr-btn {
  padding: 5px 15px;
}
.setSaddr-btn-right {
  padding: 15px 10px 0;
  text-align: right;
}
</style>
