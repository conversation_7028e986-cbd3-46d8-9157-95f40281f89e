import { Cell, Graph, Node } from "@antv/x6";
import {
  CellEquipmentData,
  EquipmentConfig,
  EquipmentData,
  EquipmentShowType,
  EquipmentType,
  RenderData,
  RenderItem
} from "./Graph";
import {
  equipmentDataToCell,
  getCellDataEquipmentConfig,
  getCellDataEquipmentData,
  isCbrDis,
  renderGroupCells
} from "./GraphUtil";
/**
 *
 * <AUTHOR>
 * @version 1.0
 */
class GraphFactory {
  equipmentDataMap: Map<string, EquipmentData>;
  constructor() {
    this.equipmentDataMap = new Map();
  }
  /**
   * 处理短地址对应图符
   * @param graph
   * @param equipmentDatas
   * @returns
   */
  render(graph: Graph, equipmentDatas: EquipmentData[]): RenderData {
    equipmentDatas.forEach(item => {
      this.equipmentDataMap.set(item.id, item);
    });
    const allCells = graph.getCells();
    const renderData: RenderData = {
      yxMap: new Map()
    };
    // 图符预处理
    this.handleSaddrCells(allCells, graph, renderData);
    return renderData;
  }
  private handleSaddrCells(cells: Cell[], graph: Graph, renderData: RenderData) {
    for (const cell of cells) {
      this.handleSaddrCell(cell, graph, renderData);
    }
  }
  private handleSaddrCell(cell: Cell, graph: Graph, renderData: RenderData) {
    const equipmentConfig = getCellDataEquipmentConfig(cell);
    if (!equipmentConfig) {
      return;
    }
    this.handleYx(cell, graph, equipmentConfig, renderData);
  }
  private handleYx(cell: Cell, graph: Graph, equipmentConfig: EquipmentConfig, renderData: RenderData) {
    const node = cell as Node;
    const renderItem: RenderItem = {
      cell: cell,
      equipmentConfig: equipmentConfig,
      show: []
    };
    let renderItems = renderData.yxMap.get(equipmentConfig.saddr);
    if (!renderItems) {
      renderItems = [];
      renderData.yxMap.set(equipmentConfig.saddr, renderItems);
    }
    renderItems.push(renderItem);
    // 如果配置了自定义显示，优先级最高
    if (equipmentConfig.showCfg && equipmentConfig.showCfg.length > 0) {
      for (const item of equipmentConfig.showCfg) {
        if (item.type == EquipmentShowType.EQUIPMENT) {
          const allCells = [...equipmentDataToCell(item.showValue as EquipmentData, graph)];
          // 自定义图符限制只支持一个图符的
          const refCells = allCells[0];
          const refNode = refCells[0] as Node;
          // 添加图符到该设备位置处，并隐藏
          refNode.setPosition(node.position());
          refNode.setVisible(false);
          graph.addCell(refNode);
          // 判断是否是组合，需要添加子元素
          if (refCells.length > 1) {
            renderGroupCells(graph, refCells.slice(1), refNode, false);
          }
          // 设置数据
          renderItem.show?.push({
            value: item.value,
            showValue: refNode
          });
        } else if (item.type == EquipmentShowType.TEXT) {
          // 设置数据
          renderItem.show?.push({
            value: item.value,
            showValue: item.showValue as string
          });
        }
      }
      return;
    }
    const dataEquipmentData = getCellDataEquipmentData(cell);
    if (!dataEquipmentData) {
      return;
    }
    // 检查是否是道闸
    if (isCbrDis(dataEquipmentData.equipmentType as EquipmentType)) {
      this.appendCbrDisCell(graph, cell, dataEquipmentData, renderItem);
    }
  }
  private appendCbrDisCell(graph: Graph, cell: Cell, dataEquipmentData: CellEquipmentData, renderItem: RenderItem) {
    const cellDataEquipmentData = getCellDataEquipmentData(cell);
    if (!cellDataEquipmentData) {
      console.log("组件data属性中未找到equipemntData数据", cell);
      return;
    }
    const equipmentData = this.equipmentDataMap.get(cellDataEquipmentData.equipmentId);
    if (!equipmentData || equipmentData.components.length < 2) {
      console.log("开关或刀闸组件不存在或个数<2", equipmentData, cell.id);
      return;
    }
    const index = dataEquipmentData.equipmentIndex == 0 ? 1 : 0;
    let refNodes: Cell[] = [];
    if (dataEquipmentData.loadType == "create") {
      const figure = equipmentData.components[index];
      const createNode = graph.createNode(figure.data.value as Node.Metadata);
      refNodes.push(createNode);
      // 添加的图符，不设置data数据
    } else {
      const cells = [...equipmentDataToCell(equipmentData, graph)];
      refNodes = cells[index];
    }
    // 是否需要clone
    const refNode = refNodes[0] as Node;
    // 添加图符到该设备位置处，并隐藏
    refNode.setPosition((cell as Node).position());
    refNode.setVisible(false);
    graph.addCell(refNode);
    // 判断是否是组合组件
    if (refNodes.length > 1) {
      const childCells = refNodes.slice(1);
      renderGroupCells(graph, childCells, refNode, false);
    }
    renderItem.show?.push({
      value: equipmentData.components[index].equipmentStatus,
      showValue: refNode,
      isCbrDis: true
    });
  }
}
export { GraphFactory };
