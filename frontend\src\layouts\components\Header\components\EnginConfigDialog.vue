<template>
  <div class="engin-main">
    <el-dialog v-model="dialogVisible" width="560px" :title="title" draggable>
      <template #header="{ titleId, titleClass }">
        <div class="dialog-header">
          <svg-icon :icon="dialogIcon" />
          <span :id="titleId" :class="titleClass">{{ title }}</span>
        </div>
      </template>
      <div class="engin-root">
        <div class="engin-type">
          <span>{{ t("layout.header.enginConfig.configType") }}</span>
          <el-select v-model="configType" placeholder="请选择配置类型">
            <el-option v-for="item in configTypes" :key="item.value" :label="item.label" :value="item.value">
              <template #default>
                <svg-icon v-if="item.type === 'all'" icon="ep:menu" style="margin-right: 8px; font-size: 14px" />
                <svg-icon v-else-if="item.type === 'device'" icon="ep:cpu" style="margin-right: 8px; font-size: 14px" />
                <svg-icon v-else-if="item.type === 'configure'" icon="ep:list" style="margin-right: 8px; font-size: 14px" />
                <span>{{ item.label }}</span>
              </template>
            </el-option>
          </el-select>
        </div>

        <div class="content-box">
          <el-input v-model="filePath" :placeholder="placeholder" :readonly="true" size="default">
            <template #append>
              <el-tooltip
                :content="t('layout.header.enginConfig.openDirectory')"
                popper-class="is-small"
                transition="none"
                effect="light"
                placement="top"
                :show-after="500"
                :hide-after="0"
              >
                <el-button :icon="Folder" @click="openDirectory" />
              </el-tooltip>
            </template>
          </el-input>
        </div>

        <div class="engin-button">
          <el-button @click="cancel">{{ t("layout.header.enginConfig.cancel") }}</el-button>
          <el-button type="primary" @click="confirmMain">{{ t("layout.header.enginConfig.confirm") }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { Folder } from "@element-plus/icons-vue";
import { Message } from "@/scripts/message";
import { osControlApi } from "@/api/modules/biz/os";
import { isEmpty, find, filter } from "lodash";
import { moreControlApi } from "@/api/modules/biz/more";
import { MoreInfo } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
import mittBus from "@/utils/mittBus";
import { useHmiStore } from "@/stores/modules";
import { ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const debugStore = useDebugStore();
const hmiStore = useHmiStore();
const props = defineProps<{
  enginConfigParam: { type: string; title: string; placeholder: string };
}>();
const dialogVisible = ref(false);
const configType = ref(0);
const configTypes = computed(() => [
  {
    value: 0,
    label: t("layout.header.enginConfig.all"),
    type: "all"
  },
  {
    value: 1,
    label: t("layout.header.enginConfig.deviceList"),
    type: "device"
  },
  {
    value: 2,
    label: t("layout.header.enginConfig.configureList"),
    type: "configure"
  }
]);
const title = computed(() => {
  return props.enginConfigParam.title;
});
const placeholder = computed(() => {
  return props.enginConfigParam.placeholder;
});
const dialogIcon = computed(() => {
  return props.enginConfigParam.type === "import" ? "ep:upload" : "ep:download";
});
const filePath = ref<string>("");

const confirmMain = async (): Promise<void> => {
  const success = await confirm();
  if (success) {
    dialogVisible.value = false;
  }
};
const confirm = async (): Promise<boolean> => {
  const config = find(configTypes.value, { value: configType.value }) as
    | { value: number; label: string; type: string }
    | undefined;
  if (!config) {
    return false;
  }
  if (props.enginConfigParam.type == "export") {
    if (isEmpty(filePath.value)) {
      addWarningTips(t("services.more.exportPathNotExists"));
      return false;
    }
    const req: MoreInfo.ExportConfigParam = { targetPath: filePath.value, type: config.type };
    const res: any = await moreControlApi.exportConfig(req);
    if (res.code == 0) {
      addSuccessTips(t("layout.header.enginConfig.exportSuccess"));
      return true;
    }
    addWarningTips(res.msg);
    return false;
  }
  if (isEmpty(filePath.value)) {
    addWarningTips(t("services.more.importPathNotExists"));
    return false;
  }
  if (!(await checkData(config.type))) {
    return false;
  }
  const req: MoreInfo.ImportConfigParam = { sourcePath: filePath.value, type: config.type };
  const res: any = await moreControlApi.importConfig(req);
  if (res.code == 0) {
    addSuccessTips(t("layout.header.enginConfig.importSuccess"));
    notifyImport(config.type);
    return true;
  }
  addWarningTips(res.msg);
  return false;
};

const checkData = async (type: string): Promise<boolean> => {
  if (type === "device") {
    return checkDevice();
  } else if (type === "configure") {
    return checkConfigure();
  }
  const resDevice = await checkDevice();
  const resConfigure = await checkConfigure();
  return resDevice && resConfigure;
};
const checkDevice = async (): Promise<boolean> => {
  if (debugStore.deviceList.length > 0) {
    const connectedList = filter(debugStore.deviceList, { isConnect: true });
    if (connectedList && connectedList.length > 0) {
      Message.warning(t("layout.header.enginConfig.disconnectDeviceFirst"));
      return false;
    }
  }
  return true;
};
const checkConfigure = async (): Promise<boolean> => {
  const res = ref(false);
  const confList = hmiStore.hmiInfo.configureList;
  // 安全检查：确保confList存在且第一个元素存在且有children属性
  if (confList && confList.length > 0 && confList[0] && confList[0].children && confList[0].children.length > 0) {
    await ElMessageBox.confirm(t("layout.header.enginConfig.overrideConfirm"), t("layout.header.enginConfig.warmTips"), {
      confirmButtonText: t("layout.header.enginConfig.confirm"),
      cancelButtonText: t("layout.header.enginConfig.cancel"),
      type: "warning"
    })
      .then(async () => {
        res.value = true;
      })
      .catch(() => {
        // 用户取消操作，res.value 保持 false
      });
  } else {
    res.value = true;
  }
  return res.value;
};

const addSuccessTips = (msg: string) => {
  Message.success(msg);
  debugStore.addConsole(msg);
};
const addWarningTips = (msg: string) => {
  Message.warning(msg);
  debugStore.addConsole(msg);
};

const cancel = async (): Promise<void> => {
  dialogVisible.value = false;
};

const openDirectory = async (): Promise<void> => {
  const config = find(configTypes.value, { value: configType.value }) as
    | { value: number; label: string; type: string }
    | undefined;
  if (!config) {
    return;
  }

  // 导出时选择文件夹
  if (props.enginConfigParam.type == "export") {
    const res: any = await osControlApi.selectFolder();
    const path: string = res;
    if (!isEmpty(path)) {
      filePath.value = path;
    }
    return;
  }

  // 导入时选择压缩包文件
  const res: any = await osControlApi.selectFileByParams({
    title: t("layout.header.enginConfig.importConfigFile"),
    filterList: [
      { name: t("layout.header.enginConfig.zipFiles"), extensions: ["zip"] },
      { name: t("layout.header.enginConfig.allFiles"), extensions: ["*"] }
    ]
  });
  const path: string = res.path;
  if (!isEmpty(path)) {
    filePath.value = path;
  }
};

const notifyImport = (type: string) => {
  mittBus.emit("afterImportConfig", type);
};

const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });

watch(
  () => dialogVisible.value,
  newValue => {
    if (newValue == true) {
      filePath.value = "";
    }
  }
);
</script>

<style lang="scss" scoped>
.engin-main {
  :deep(.el-dialog__header) {
    padding: 8px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  :deep(.el-dialog__body) {
    padding: 24px;
  }
  .dialog-header {
    display: flex;
    align-items: center;
    font-weight: 500;

    svg {
      margin-right: 8px;
      color: var(--el-color-primary);
    }
  }
  .engin-root {
    .engin-type {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-weight: 500;
      color: var(--el-text-color-regular);

      .el-select {
        margin-left: 12px;
        width: 320px;
      }
    }
    .content-box {
      margin-bottom: 20px;

      .el-input {
        :deep(.el-input__wrapper) {
          border-radius: 6px;
        }
        :deep(.el-input-group__append) {
          border-radius: 0 6px 6px 0;
        }
      }
    }
    .engin-button {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);

      .el-button {
        min-width: 80px;
      }
    }
  }
}
</style>
