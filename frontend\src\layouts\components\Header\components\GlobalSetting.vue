<template>
  <div class="item-main" :class="{ active: isActive }" @click="openSetting">
    <div class="setting-trigger">
      <el-tooltip :content="t('layout.header.globalSetting.tooltip')" :placement="getTooltipPlacement()" :show-after="500">
        <i class="iconfont toolBar-icon" style="display: flex; flex-direction: column">
          <svg-icon icon="ant-design:setting-filled"></svg-icon>
          <span v-if="globalStore.checkColumnLayout()" style="margin-top: 6px; font-size: 12px">{{
            t("layout.header.globalSetting.title")
          }}</span>
        </i>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useGlobalStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
// import Message from "@/scripts/message";

const { t } = useI18n();
const globalStore = useGlobalStore();
// const place = defineModel<toolTipTypes>();
const router = useRouter();
const route = useRoute();

// 检查是否在设置页面
const isActive = computed(() => {
  return route.path.startsWith("/sys/config");
});

const openSetting = () => {
  router.push("/sys/config/index");
  // Message.warning("待开发");
};

// 根据布局类型动态获取tooltip位置
const getTooltipPlacement = (): toolTipTypes => {
  const layout = globalStore.layout;
  // 横向布局时tooltip显示在底部，其他布局显示在右侧
  return layout === "transverse" ? "bottom" : "right";
};
</script>

<style scoped lang="scss">
.item-main {
  display: flex;
  flex-flow: column wrap;
  place-content: space-around center;
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;

  // 选中状态样式，使用菜单主题变量与左侧主菜单保持一致
  &.active {
    background-color: var(--el-menu-active-bg-color) !important;
    .iconfont,
    .toolBar-icon,
    svg,
    span {
      color: var(--el-menu-active-color) !important;
    }
    .setting-trigger {
      .iconfont,
      svg,
      span {
        color: var(--el-menu-active-color) !important;
      }
    }
  }
}
.setting-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
}
.toolBar-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
