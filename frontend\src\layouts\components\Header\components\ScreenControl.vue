<template>
  <div>
    <el-tooltip :content="t('layout.header.minimize')" placement="bottom" effect="dark">
      <el-button text :icon="Minus" class="toolBar-icon" @click="minimizeWindow"></el-button>
    </el-tooltip>
  </div>
  <div>
    <el-tooltip
      :content="isMaximized ? t('layout.header.restore') : t('layout.header.maximize')"
      placement="bottom"
      effect="dark"
    >
      <el-button text :icon="isMaximized ? CopyDocument : FullScreen" class="toolBar-icon" @click="maximizeWindow"></el-button>
    </el-tooltip>
  </div>
  <div>
    <el-tooltip :content="t('layout.header.close')" placement="bottom" effect="dark">
      <el-button text :icon="Close" class="toolBar-icon" @click="closeWindow"></el-button>
    </el-tooltip>
  </div>

  <Closer v-if="dialogShow.closeDialog" v-model="dialogShow.closeDialog"></Closer>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";
import { Close, FullScreen, Minus, CopyDocument } from "@element-plus/icons-vue";
import { windowControlApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const dialogShow = ref({
  closeDialog: false
});

// 窗口最大化状态
const isMaximized = ref(false);

// 检查窗口状态的函数
const checkWindowState = () => {
  // 通过检查窗口大小来判断是否最大化
  const isFullscreen = window.innerWidth === screen.width && window.innerHeight === screen.height;
  const isNearFullscreen = window.innerWidth >= screen.width * 0.95 && window.innerHeight >= screen.height * 0.9;
  isMaximized.value = isFullscreen || isNearFullscreen;
};

const minimizeWindow = () => {
  windowControlApi.minimizeWindow();
};

const maximizeWindow = async () => {
  await windowControlApi.maximizeWindow();
  // 延迟检查状态，等待窗口状态改变
  setTimeout(checkWindowState, 100);
};

const closeWindow = () => {
  dialogShow.value.closeDialog = true;
};

// 监听窗口大小变化
const handleResize = () => {
  checkWindowState();
};

// 组件挂载时检查初始状态并添加监听器
onMounted(() => {
  checkWindowState();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时移除监听器
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});
</script>
