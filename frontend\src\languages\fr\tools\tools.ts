export default {
  search: {
    placeholder: "Rechercher par mot-clé"
  },
  categories: {
    title: "📦Outils IT",
    formatting: "📝Outils de formatage",
    xml: "🟡Formatage XML",
    json: "🟡Formatage JSON",
    conversion: "🔄Outils de conversion",
    radix: "🟢Conversion de base",
    temperature: "🟢Conversion de température",
    encryption: "🔑Outils de chiffrement",
    textEncryption: "🔵Chiffrement/déchiffrement de texte"
  },
  encryption: {
    title: "Chiffrement/déchiffrement de texte",
    description:
      "Chiffrer et déchiffrer du texte en clair en utilisant des algorithmes de chiffrement (comme AES, TripleDES, Rabbit ou RC4)",
    encrypt: "Chiffrer",
    inputText: "Texte à chiffrer :",
    inputPlaceholder: "Veuillez entrer le contenu du texte à chiffrer...",
    key: "Clé :",
    keyPlaceholder: "Veuillez entrer la clé de chiffrement",
    algorithm: "Algorithme de chiffrement :",
    outputText: "Texte chiffré :",
    outputPlaceholder: "Le résultat du chiffrement s'affichera ici...",
    decrypt: "Déchiffrer",
    decryptInputText: "Texte à déchiffrer :",
    decryptInputPlaceholder: "Veuillez entrer le texte chiffré à déchiffrer...",
    decryptKey: "Clé :",
    decryptAlgorithm: "Algorithme de déchiffrement :",
    decryptOutputText: "Texte déchiffré :",
    decryptError: "Impossible de déchiffrer le texte"
  },
  json: {
    title: "Formatage JSON",
    description: "Formater une chaîne JSON en format lisible et convivial",
    sortKeys: "Tri des champs",
    indentSize: "Taille d'indentation",
    inputLabel: "JSON à formater",
    inputPlaceholder: "Collez votre JSON...",
    outputLabel: "JSON formaté",
    invalid: "Ce document ne respecte pas la spécification JSON, veuillez vérifier"
  },
  xml: {
    title: "Formatage XML",
    description: "Formater une chaîne XML en format lisible et convivial",
    collapseContent: "Réduire le contenu :",
    indentSize: "Taille d'indentation :",
    inputLabel: "Entrer le XML",
    inputPlaceholder: "Collez votre XML...",
    outputLabel: "XML formaté",
    invalid: "Ce document ne respecte pas la spécification XML, veuillez vérifier"
  },
  temperature: {
    title: "Conversion de température",
    description: "Conversion entre Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur et Rømer",
    kelvin: "Kelvin",
    kelvinUnit: "K",
    celsius: "Celsius",
    celsiusUnit: "°C",
    fahrenheit: "Fahrenheit",
    fahrenheitUnit: "°F",
    rankine: "Rankine",
    rankineUnit: "°R",
    delisle: "Delisle",
    delisleUnit: "°De",
    newton: "Newton",
    newtonUnit: "°N",
    reaumur: "Réaumur",
    reaumurUnit: "°Ré",
    romer: "Rømer",
    romerUnit: "°Rø"
  },
  radix: {
    title: "Conversion de base",
    description: "Convertir des nombres entre différentes bases (décimal, hexadécimal, binaire, octal, base64...)",
    inputLabel: "Nombre à convertir",
    inputPlaceholder: "Entrez un nombre (ex: 100)",
    outputLabel: "Résultat de conversion",
    binary: "Base 2 (2)",
    binaryPlaceholder: "Résultat binaire...",
    octal: "Base 8 (8)",
    octalPlaceholder: "Résultat octal...",
    decimal: "Base 10 (10)",
    decimalPlaceholder: "Résultat décimal...",
    hex: "Base 16 (16)",
    hexPlaceholder: "Résultat hexadécimal...",
    base64: "Base64 (64)",
    base64Placeholder: "Résultat Base64...",
    customBase: "Base personnalisée",
    customBasePlaceholder: "Résultat Base {{base}}..."
  },
  jsonViewer: {
    title: "Formatage JSON",
    description: "Formater une chaîne JSON en format lisible et convivial",
    sortKeys: "Tri des champs",
    indentSize: "Taille d'indentation",
    inputJson: "JSON à formater",
    formattedJson: "JSON formaté",
    placeholder: "Collez votre JSON...",
    validationError: "Ce document ne respecte pas la spécification JSON. Veuillez vérifier"
  }
};
