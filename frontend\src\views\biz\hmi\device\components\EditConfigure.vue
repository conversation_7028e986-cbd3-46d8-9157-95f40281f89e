<template>
  <div class="edit-configure-container">
    <div class="edit-configure-main" :style="configureMainStyle.mainRoot">
      <GraphEditor
        :data="form.editorData"
        @save="saveEquipment"
        @get-equipment-list="getEquipmentList"
        ref="graphEditor"
      ></GraphEditor>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore, useHmiStore } from "@/stores/modules";
import { ResultData } from "@/api";
import { Message } from "@/scripts/message";
import { configureInfoApi } from "@/api/modules/biz/hmi";
import { graphDefineApi } from "@/api/modules/biz/hmi";
import { Configure } from "@/api/interface/biz/hmi";
import GraphEditor from "@/views/biz/hmi/packages/components/graph-editor/src/GraphEditor.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const graphEditor = ref();
const globalStore = useGlobalStore();
const hmiStore = useHmiStore();

const form = ref({
  editorData: {
    graphData: undefined,
    equipmentDatas: []
  }
});

const configureMainStyle = computed<any>(() => {
  let offset = 98;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return { mainRoot: { height: `calc(100vh  - ${offset}px)` } };
});

const saveEquipment = async equipmentData => {
  const project: Configure.SaveConfigureInfo = {
    id: hmiStore.hmiInfo.currConfigure.id,
    path: hmiStore.hmiInfo.currConfigure.path!,
    label: hmiStore.hmiInfo.currConfigure.label!,
    data: JSON.stringify(equipmentData)
  };
  const res: ResultData = await configureInfoApi.saveConfigure(project);
  if (res.code == 0) {
    Message.success(t("hmi.device.editConfigure.saveSuccess"));
    return;
  }
  Message.error(t("hmi.device.editConfigure.saveFailed") + res.msg);
};

const loadEquipment = async () => {
  const project: Configure.LoadConfigureInfo = {
    id: hmiStore.hmiInfo.currConfigure.id,
    path: hmiStore.hmiInfo.currConfigure.path!,
    label: hmiStore.hmiInfo.currConfigure.label!
  };
  const res: ResultData = await configureInfoApi.loadConfigure(project);
  if (res.code != 0) {
    Message.error(t("hmi.device.editConfigure.loadFailed") + res.msg);
    return;
  }
  if (graphEditor.value) {
    graphEditor.value.setGraphData(res.data);
  }
};
/**
 * 获取自定义图符
 */
const getEquipment = async () => {
  const result = await graphDefineApi.get();
  if (result.code != 0) {
    ElMessageBox.alert(t("hmi.device.editConfigure.getCustomDeviceFailed"), {
      title: t("hmi.device.editConfigure.tip"),
      type: "error"
    });
    return;
  }
  if (result.data) {
    form.value.editorData.equipmentDatas = result.data;
  }
};
/**
 * 关联短地址时触发的获取自定义图符
 */
const getEquipmentList = async () => {
  await getEquipment();
  if (graphEditor) {
    graphEditor.value.setEquipmentList(form.value.editorData.equipmentDatas);
  }
};

onMounted(() => {
  loadEquipment();
  getEquipment();
});

watch(
  () => hmiStore.hmiInfo.currConfigure.id,
  () => {
    loadEquipment();
  }
);
</script>

<style scoped lang="scss">
.edit-configure-container {
  flex-direction: row;
  width: 100%;
  .edit-configure-main {
    width: 100%;
  }
}
</style>
