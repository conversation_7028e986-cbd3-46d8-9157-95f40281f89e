<template>
  <el-dialog
    class="remote-control-main"
    v-model="dialogVisible"
    :title="t('layout.header.remoteControl.title')"
    width="800px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="remote-control-container">
      <!-- 连接配置区域 -->
      <el-card shadow="hover" class="config-card" v-if="!isConnected">
        <h4 class="title">{{ t("layout.header.remoteControl.connectionConfig") }}</h4>
        <el-form :model="connectionForm" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.host')" prop="host">
                <el-input v-model="connectionForm.host" :placeholder="t('layout.header.remoteControl.hostPlaceholder')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.port')" prop="port">
                <el-input-number v-model="connectionForm.port" :min="1" :max="65535" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.username')" prop="username">
                <el-input v-model="connectionForm.username" :placeholder="t('layout.header.remoteControl.usernamePlaceholder')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.password')" prop="password">
                <el-input
                  v-model="connectionForm.password"
                  type="password"
                  :placeholder="t('layout.header.remoteControl.passwordPlaceholder')"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="connectVNC" :loading="connecting">
              {{ t("layout.header.remoteControl.connect") }}
            </el-button>
            <el-button @click="resetForm">{{ t("layout.header.remoteControl.reset") }}</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- VNC 显示区域 -->
      <div class="vnc-container" v-if="isConnected">
        <div class="vnc-toolbar">
          <el-button-group>
            <el-button size="small" @click="sendCtrlAltDel">
              <el-icon><Monitor /></el-icon>
              Ctrl+Alt+Del
            </el-button>
            <el-button size="small" @click="toggleFullscreen">
              <el-icon><FullScreen /></el-icon>
              {{ t("layout.header.remoteControl.fullscreen") }}
            </el-button>
            <el-button size="small" @click="takeScreenshot">
              <el-icon><Camera /></el-icon>
              {{ t("layout.header.remoteControl.screenshot") }}
            </el-button>
            <el-button size="small" type="danger" @click="disconnectVNC">
              <el-icon><Close /></el-icon>
              {{ t("layout.header.remoteControl.disconnect") }}
            </el-button>
          </el-button-group>
          <div class="connection-info">
            <el-tag type="success">
              <el-icon><Connection /></el-icon>
              {{ connectionForm.host }}:{{ connectionForm.port }}
            </el-tag>
          </div>
        </div>
        <div class="vnc-display" ref="vncDisplay" id="vnc-display">
          <!-- VNC 画面将在这里显示 -->
          <div class="vnc-placeholder" v-if="!vncReady">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <p>{{ t("layout.header.remoteControl.connecting") }}</p>
          </div>
        </div>
      </div>

      <!-- 连接状态信息 -->
      <div class="status-info" v-if="statusMessage">
        <el-alert :title="statusMessage" :type="statusType" :closable="false" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
// 导入 NoVNC 类型
import type RFB from "@novnc/novnc/lib/rfb";

import { ref, reactive, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { Monitor, FullScreen, Camera, Close, Connection, Loading } from "@element-plus/icons-vue";
import { remoteControlApi } from "@/api/modules/sys/remote";

const { t } = useI18n();
const dialogVisible = ref(false);
const isConnected = ref(false);
const connecting = ref(false);
const vncReady = ref(false);
const statusMessage = ref("");
const statusType = ref<"success" | "warning" | "info" | "error">("info");

const formRef = ref();
const vncDisplay = ref();

// 连接表单数据
const connectionForm = reactive({
  host: "*************",
  port: 5900,
  username: "",
  password: ""
});

// 表单验证规则
const rules = {
  host: [
    { required: true, message: t("layout.header.remoteControl.hostRequired"), trigger: "blur" },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9.-]+$/,
      message: t("layout.header.remoteControl.hostInvalid"),
      trigger: "blur"
    }
  ],
  port: [{ required: true, message: t("layout.header.remoteControl.portRequired"), trigger: "blur" }]
};

// VNC 连接对象和会话信息
let vncClient: RFB | null = null;
let currentSession: any = null;
let websocketUrl: string = "";

const openDialog = () => {
  dialogVisible.value = true;
  loadVNCLibrary();
};

// 动态加载 VNC 库
const loadVNCLibrary = async () => {
  try {
    // 检查是否已经加载了 noVNC 库
    if (window.RFB) {
      console.log("noVNC library already loaded");
      return;
    }

    // 尝试动态导入 noVNC 库
    try {
      // cspell:disable-next-line
      const { default: RFB } = await import("@novnc/novnc/lib/rfb");
      window.RFB = RFB;
      console.log("noVNC library loaded successfully");
    } catch (importError) {
      // 如果导入失败，提示用户安装依赖
      console.warn("noVNC library not found, please install @novnc/novnc package");
      ElMessage.warning("请先安装 noVNC 依赖包: npm install @novnc/novnc");
      throw new Error("noVNC library not available");
    }
  } catch (error: any) {
    console.error("Failed to load VNC library:", error);
    ElMessage.error(error.message || t("layout.header.remoteControl.libraryLoadError"));
    throw error;
  }
};

// 连接 VNC
const connectVNC = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    connecting.value = true;
    statusMessage.value = t("layout.header.remoteControl.connecting");
    statusType.value = "info";

    // 首先测试连接
    const testResult = await remoteControlApi.testConnection({
      host: connectionForm.host,
      port: connectionForm.port,
      username: connectionForm.username,
      password: connectionForm.password
    });

    if (!testResult.data) {
      throw new Error("无法连接到目标主机");
    }

    // 建立VNC连接
    const connectResult = await remoteControlApi.connect({
      host: connectionForm.host,
      port: connectionForm.port,
      username: connectionForm.username,
      password: connectionForm.password
    });

    if (connectResult.data) {
      currentSession = connectResult.data;
      websocketUrl = connectResult.data.websocketUrl || `ws://${connectionForm.host}:${connectionForm.port + 1}`;

      // 使用noVNC建立连接
      await initNoVNCConnection();
    } else {
      throw new Error("连接建立失败");
    }
  } catch (error: any) {
    console.error("VNC connection failed:", error);
    statusMessage.value = error.message || t("layout.header.remoteControl.connectFailed");
    statusType.value = "error";
    ElMessage.error(error.message || t("layout.header.remoteControl.connectError"));
  } finally {
    connecting.value = false;
  }
};

// 断开 VNC 连接
const disconnectVNC = async () => {
  try {
    const result = await ElMessageBox.confirm(
      t("layout.header.remoteControl.disconnectConfirm"),
      t("layout.header.remoteControl.warning"),
      {
        confirmButtonText: t("layout.header.remoteControl.confirm"),
        cancelButtonText: t("layout.header.remoteControl.cancel"),
        type: "warning"
      }
    );

    if (result === "confirm") {
      // 断开noVNC连接
      if (vncClient) {
        vncClient.disconnect();
        vncClient = null;
      }

      // 通知后端断开连接
      if (currentSession) {
        await remoteControlApi.disconnect(currentSession.sessionId);
        currentSession = null;
      }

      // 清理显示区域
      if (vncDisplay.value) {
        vncDisplay.value.innerHTML = "";
      }

      isConnected.value = false;
      vncReady.value = false;
      statusMessage.value = t("layout.header.remoteControl.disconnected");
      statusType.value = "info";

      ElMessage.success(t("layout.header.remoteControl.disconnectSuccess"));
    }
  } catch (error: any) {
    console.error("Disconnect error:", error);
    ElMessage.error("断开连接时发生错误");
  }
};

// 发送 Ctrl+Alt+Del
const sendCtrlAltDel = async () => {
  if (vncClient) {
    try {
      // 使用noVNC发送Ctrl+Alt+Del
      vncClient.sendCtrlAltDel();
      ElMessage.success(t("layout.header.remoteControl.ctrlAltDelSent"));
    } catch (error: any) {
      console.error("Send Ctrl+Alt+Del error:", error);
      ElMessage.error("发送Ctrl+Alt+Del时发生错误");
    }
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (vncDisplay.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      vncDisplay.value.requestFullscreen();
    }
  }
};

// 截图
const takeScreenshot = async () => {
  if (vncClient) {
    try {
      // 从noVNC canvas获取截图
      const canvas = vncDisplay.value?.querySelector("canvas");
      if (canvas) {
        const dataURL = canvas.toDataURL("image/png");

        // 创建下载链接
        const link = document.createElement("a");
        link.href = dataURL;
        link.download = `vnc_screenshot_${new Date().getTime()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        ElMessage.success(t("layout.header.remoteControl.screenshotTaken"));
      } else {
        ElMessage.error("无法获取画面");
      }
    } catch (error: any) {
      console.error("Screenshot error:", error);
      ElMessage.error("截图时发生错误");
    }
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  connectionForm.host = "*************";
  connectionForm.port = 5900;
  connectionForm.username = "";
  connectionForm.password = "";
};

// VNC 事件处理
const onVNCDisconnected = () => {
  isConnected.value = false;
  vncReady.value = false;
  statusMessage.value = t("layout.header.remoteControl.disconnected");
  statusType.value = "warning";
};

const onCredentialsRequired = () => {
  ElMessage.warning(t("layout.header.remoteControl.credentialsRequired"));
};

// 初始化noVNC连接
const initNoVNCConnection = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      if (!vncDisplay.value) {
        reject(new Error("VNC显示区域未找到"));
        return;
      }

      // 清空显示区域
      vncDisplay.value.innerHTML = "";

      // 创建noVNC连接
      const RFBClass = window.RFB;
      if (!RFBClass) {
        throw new Error("noVNC library not loaded");
      }

      vncClient = new RFBClass(vncDisplay.value, websocketUrl, {
        credentials: {
          username: connectionForm.username,
          password: connectionForm.password
        }
      });

      // 设置noVNC事件监听器
      vncClient.addEventListener("connect", () => {
        console.log("noVNC connected");
        isConnected.value = true;
        vncReady.value = true;
        statusMessage.value = t("layout.header.remoteControl.connected");
        statusType.value = "success";
        ElMessage.success(t("layout.header.remoteControl.connectSuccess"));
        resolve();
      });

      vncClient.addEventListener("disconnect", (e: any) => {
        console.log("noVNC disconnected:", e.detail);
        onVNCDisconnected();
      });

      vncClient.addEventListener("credentialsrequired", () => {
        console.log("noVNC credentials required");
        onCredentialsRequired();
      });

      vncClient.addEventListener("securityfailure", (e: any) => {
        console.error("noVNC security failure:", e.detail);
        statusMessage.value = "安全验证失败: " + e.detail.reason;
        statusType.value = "error";
        ElMessage.error("安全验证失败: " + e.detail.reason);
        reject(new Error("安全验证失败"));
      });

      // 设置连接超时
      setTimeout(() => {
        if (!vncReady.value) {
          reject(new Error("连接超时"));
        }
      }, 15000); // 15秒超时

      // 设置noVNC选项
      vncClient.scaleViewport = true; // 自动缩放
      vncClient.resizeSession = false; // 不调整远程会话大小
      vncClient.showDotCursor = true; // 显示光标
    } catch (error: any) {
      console.error("Init noVNC error:", error);
      reject(error);
    }
  });
};

// 组件卸载时清理资源
onUnmounted(() => {
  if (vncClient) {
    vncClient.disconnect();
    vncClient = null;
  }
});

defineExpose({ openDialog });
</script>

<style lang="scss">
.remote-control-main {
  .el-dialog__body {
    padding: 20px;
    background: #fafbfc;
    [class="dark"] & {
      background: #23272e;
    }
  }

  .remote-control-container {
    .config-card {
      margin-bottom: 20px;
      border-radius: 12px;

      .title {
        padding-left: 10px;
        margin: 0 0 20px;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-left: 4px solid var(--el-color-primary);
        [class="dark"] & {
          color: #8cd2ff;
          border-left: 4px solid #8cd2ff;
        }
      }
    }

    .vnc-container {
      .vnc-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        margin-bottom: 10px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 8px;

        .connection-info {
          .el-tag {
            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .vnc-display {
        position: relative;
        width: 100%;
        height: 500px;
        background: #000;
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        overflow: hidden;

        // noVNC样式
        :deep(.noVNC_canvas) {
          width: 100% !important;
          height: 100% !important;
        }

        :deep(.noVNC_screen) {
          width: 100% !important;
          height: 100% !important;
        }

        .vnc-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          color: #fff;

          .loading-icon {
            font-size: 48px;
            margin-bottom: 16px;
            animation: rotate 2s linear infinite;
          }

          p {
            font-size: 16px;
            margin: 0;
          }
        }
      }
    }

    .status-info {
      margin-top: 16px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 全屏样式
.vnc-display:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  border: none !important;
  border-radius: 0 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .remote-control-main {
    .vnc-container {
      .vnc-toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;

        .el-button-group {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }
      }

      .vnc-display {
        height: 300px;
      }
    }
  }
}
</style>
