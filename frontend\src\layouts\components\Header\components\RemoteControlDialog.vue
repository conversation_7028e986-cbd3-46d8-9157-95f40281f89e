<template>
  <el-dialog
    class="remote-control-main"
    v-model="dialogVisible"
    :title="t('layout.header.remoteControl.title')"
    width="900px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleDialogClose"
  >
    <div class="remote-control-container">
      <!-- 连接配置区域 -->
      <el-card shadow="hover" class="config-card" v-if="!isConnected">
        <template #header>
          <div class="card-header">
            <h4 class="title">{{ t("layout.header.remoteControl.connectionConfig") }}</h4>
            <el-button-group size="small">
              <el-button @click="loadPreset('local')">本地</el-button>
              <el-button @click="loadPreset('test')">测试</el-button>
              <el-button @click="savePreset">保存配置</el-button>
            </el-button-group>
          </div>
        </template>

        <el-form :model="connectionForm" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.host')" prop="host">
                <el-input
                  v-model="connectionForm.host"
                  :placeholder="t('layout.header.remoteControl.hostPlaceholder')"
                  @keyup.enter="connectVNC"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.port')" prop="port">
                <el-input-number
                  v-model="connectionForm.port"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                  @keyup.enter="connectVNC"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.username')" prop="username">
                <el-input
                  v-model="connectionForm.username"
                  :placeholder="t('layout.header.remoteControl.usernamePlaceholder')"
                  @keyup.enter="connectVNC"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('layout.header.remoteControl.password')" prop="password">
                <el-input
                  v-model="connectionForm.password"
                  type="password"
                  :placeholder="t('layout.header.remoteControl.passwordPlaceholder')"
                  show-password
                  @keyup.enter="connectVNC"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 高级选项 -->
          <el-collapse v-model="advancedOptionsVisible">
            <el-collapse-item title="高级选项" name="advanced">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="连接超时(秒)">
                    <el-input-number v-model="connectionForm.timeout" :min="5" :max="60" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="质量">
                    <el-select v-model="connectionForm.quality" style="width: 100%">
                      <el-option label="高质量" value="high" />
                      <el-option label="中等质量" value="medium" />
                      <el-option label="低质量" value="low" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="自动重连">
                    <el-switch v-model="connectionForm.autoReconnect" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="共享模式">
                    <el-switch v-model="connectionForm.shared" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
          </el-collapse>

          <el-form-item style="margin-top: 20px">
            <el-button type="primary" @click="connectVNC" :loading="connecting" size="large">
              <el-icon><Connection /></el-icon>
              {{ t("layout.header.remoteControl.connect") }}
            </el-button>
            <el-button @click="resetForm" size="large">
              <el-icon><Refresh /></el-icon>
              {{ t("layout.header.remoteControl.reset") }}
            </el-button>
            <el-button @click="testConnection" :loading="testing" size="large">
              <el-icon><Monitor /></el-icon>
              测试连接
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- VNC 显示区域 -->
      <div class="vnc-container" v-if="isConnected">
        <div class="vnc-toolbar">
          <div class="toolbar-left">
            <el-button-group>
              <el-button size="small" @click="sendCtrlAltDel" :disabled="!vncReady">
                <el-icon><Monitor /></el-icon>
                Ctrl+Alt+Del
              </el-button>
              <el-button size="small" @click="sendAltTab" :disabled="!vncReady">
                <el-icon><Switch /></el-icon>
                Alt+Tab
              </el-button>
              <el-button size="small" @click="sendWinKey" :disabled="!vncReady">
                <el-icon><Platform /></el-icon>
                Win
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button size="small" @click="toggleFullscreen">
                <el-icon><FullScreen /></el-icon>
                {{ isFullscreen ? "退出全屏" : t("layout.header.remoteControl.fullscreen") }}
              </el-button>
              <el-button size="small" @click="takeScreenshot" :disabled="!vncReady">
                <el-icon><Camera /></el-icon>
                {{ t("layout.header.remoteControl.screenshot") }}
              </el-button>
              <el-button size="small" @click="refreshConnection" :disabled="!vncReady" :loading="refreshing">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </el-button-group>
          </div>

          <div class="toolbar-right">
            <div class="connection-info">
              <el-tag :type="getConnectionTagType()" size="small">
                <el-icon><Connection /></el-icon>
                {{ connectionForm.host }}:{{ connectionForm.port }}
              </el-tag>
              <el-tag type="info" size="small" style="margin-left: 8px">
                {{ getConnectionDuration() }}
              </el-tag>
            </div>

            <el-button size="small" type="danger" @click="disconnectVNC">
              <el-icon><Close /></el-icon>
              {{ t("layout.header.remoteControl.disconnect") }}
            </el-button>
          </div>
        </div>

        <!-- 连接质量和性能指标 -->
        <div class="performance-bar" v-if="showPerformanceInfo">
          <div class="performance-item">
            <span>延迟: {{ latency }}ms</span>
          </div>
          <div class="performance-item">
            <span>帧率: {{ frameRate }}fps</span>
          </div>
          <div class="performance-item">
            <span>带宽: {{ bandwidth }}</span>
          </div>
          <el-button size="small" text @click="showPerformanceInfo = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <div class="vnc-display" ref="vncDisplay" id="vnc-display" @contextmenu.prevent="showContextMenu">
          <!-- VNC 画面将在这里显示 -->
          <div class="vnc-placeholder" v-if="!vncReady">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <p>{{ t("layout.header.remoteControl.connecting") }}</p>
            <el-progress :percentage="connectionProgress" :show-text="false" style="width: 200px; margin-top: 16px" />
          </div>

          <!-- 右键菜单 -->
          <div v-if="contextMenuVisible" class="context-menu" :style="contextMenuStyle">
            <div class="menu-item" @click="copyToClipboard">复制</div>
            <div class="menu-item" @click="pasteFromClipboard">粘贴</div>
            <div class="menu-divider"></div>
            <div class="menu-item" @click="showPerformanceInfo = true">性能信息</div>
            <div class="menu-item" @click="showSettings">设置</div>
          </div>
        </div>
      </div>

      <!-- 连接状态信息 -->
      <div class="status-info" v-if="statusMessage">
        <el-alert :title="statusMessage" :type="statusType" :closable="false" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
// 导入 NoVNC 类型
import type RFB from "@novnc/novnc/lib/rfb";

import { ref, reactive, onUnmounted, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { Monitor, FullScreen, Camera, Close, Connection, Loading, Refresh, Switch, Platform } from "@element-plus/icons-vue";
import { remoteControlApi } from "@/api/modules/sys/remote";
import { useLocalStorage } from "@vueuse/core";

const { t } = useI18n();

// 对话框状态
const dialogVisible = ref(false);
const isConnected = ref(false);
const connecting = ref(false);
const testing = ref(false);
const refreshing = ref(false);
const vncReady = ref(false);
const isFullscreen = ref(false);
const connectionProgress = ref(0);

// 状态信息
const statusMessage = ref("");
const statusType = ref<"success" | "warning" | "info" | "error">("info");

// 性能监控
const showPerformanceInfo = ref(false);
const latency = ref(0);
const frameRate = ref(0);
const bandwidth = ref("0 KB/s");
const connectionStartTime = ref<Date | null>(null);

// 右键菜单
const contextMenuVisible = ref(false);
const contextMenuStyle = ref({});

// 高级选项
const advancedOptionsVisible = ref([]);

// DOM引用
const formRef = ref();
const vncDisplay = ref();

// 本地存储的配置预设
const savedPresets = useLocalStorage("vnc-presets", {
  local: { host: "127.0.0.1", port: 5900, username: "", password: "" },
  test: { host: "*************", port: 5900, username: "test", password: "test" }
});

// 连接表单数据
const connectionForm = reactive({
  host: "*************",
  port: 5900,
  username: "",
  password: "",
  timeout: 15,
  quality: "medium",
  autoReconnect: true,
  shared: false
});

// 表单验证规则
const rules = {
  host: [
    { required: true, message: t("layout.header.remoteControl.hostRequired"), trigger: "blur" },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9.-]+$/,
      message: t("layout.header.remoteControl.hostInvalid"),
      trigger: "blur"
    }
  ],
  port: [{ required: true, message: t("layout.header.remoteControl.portRequired"), trigger: "blur" }]
};

// VNC 连接对象和会话信息
let vncClient: RFB | null = null;
let currentSession: any = null;
let websocketUrl: string = "";
let performanceTimer: NodeJS.Timeout | null = null;
let reconnectTimer: NodeJS.Timeout | null = null;

// 计算属性
const getConnectionTagType = () => {
  if (!isConnected.value) return "info";
  return vncReady.value ? "success" : "warning";
};

const getConnectionDuration = computed(() => {
  if (!connectionStartTime.value) return "00:00";
  const now = new Date();
  const diff = Math.floor((now.getTime() - connectionStartTime.value.getTime()) / 1000);
  const minutes = Math.floor(diff / 60);
  const seconds = diff % 60;
  return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
});

const openDialog = () => {
  dialogVisible.value = true;
  loadVNCLibrary();
  startPerformanceMonitoring();
};

// 性能监控
const startPerformanceMonitoring = () => {
  if (performanceTimer) clearInterval(performanceTimer);
  performanceTimer = setInterval(() => {
    if (vncReady.value) {
      // 模拟性能数据更新
      latency.value = Math.floor(Math.random() * 50) + 10;
      frameRate.value = Math.floor(Math.random() * 10) + 25;
      bandwidth.value = `${Math.floor(Math.random() * 500) + 100} KB/s`;
    }
  }, 1000);
};

// 停止性能监控
const stopPerformanceMonitoring = () => {
  if (performanceTimer) {
    clearInterval(performanceTimer);
    performanceTimer = null;
  }
};

// 加载预设配置
const loadPreset = (presetName: string) => {
  const preset = savedPresets.value[presetName];
  if (preset) {
    Object.assign(connectionForm, preset);
    ElMessage.success(`已加载 ${presetName} 配置`);
  }
};

// 保存当前配置为预设
const savePreset = async () => {
  try {
    const { value: presetName } = await ElMessageBox.prompt("请输入预设名称", "保存配置", {
      confirmButtonText: "保存",
      cancelButtonText: "取消",
      inputPattern: /^[a-zA-Z0-9_-]+$/,
      inputErrorMessage: "预设名称只能包含字母、数字、下划线和横线"
    });

    if (presetName) {
      savedPresets.value[presetName] = {
        host: connectionForm.host,
        port: connectionForm.port,
        username: connectionForm.username,
        password: connectionForm.password
      };
      ElMessage.success(`配置已保存为 ${presetName}`);
    }
  } catch {
    // 用户取消
  }
};

// 测试连接
const testConnection = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    testing.value = true;
    statusMessage.value = "正在测试连接...";
    statusType.value = "info";

    const result = await remoteControlApi.testConnection({
      host: connectionForm.host,
      port: connectionForm.port,
      username: connectionForm.username,
      password: connectionForm.password
    });

    if (result.data) {
      ElMessage.success("连接测试成功！");
      statusMessage.value = "连接测试成功";
      statusType.value = "success";
    } else {
      ElMessage.error("连接测试失败");
      statusMessage.value = "连接测试失败";
      statusType.value = "error";
    }
  } catch (error: any) {
    console.error("Test connection failed:", error);
    ElMessage.error(error.message || "连接测试失败");
    statusMessage.value = error.message || "连接测试失败";
    statusType.value = "error";
  } finally {
    testing.value = false;
  }
};

// 动态加载 VNC 库
const loadVNCLibrary = async () => {
  try {
    // 检查是否已经加载了 noVNC 库
    if (window.RFB) {
      console.log("noVNC library already loaded");
      return;
    }

    // 尝试动态导入 noVNC 库
    try {
      // cspell:disable-next-line
      const { default: RFB } = await import("@novnc/novnc/lib/rfb");
      window.RFB = RFB;
      console.log("noVNC library loaded successfully");
    } catch (importError) {
      // 如果导入失败，提示用户安装依赖
      console.warn("noVNC library not found, please install @novnc/novnc package");
      ElMessage.warning("请先安装 noVNC 依赖包: npm install @novnc/novnc");
      throw new Error("noVNC library not available");
    }
  } catch (error: any) {
    console.error("Failed to load VNC library:", error);
    ElMessage.error(error.message || t("layout.header.remoteControl.libraryLoadError"));
    throw error;
  }
};

// 连接 VNC
const connectVNC = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    connecting.value = true;
    connectionProgress.value = 0;
    statusMessage.value = t("layout.header.remoteControl.connecting");
    statusType.value = "info";

    // 进度更新
    const updateProgress = (progress: number, message: string) => {
      connectionProgress.value = progress;
      statusMessage.value = message;
    };

    updateProgress(20, "正在测试连接...");

    // 首先测试连接
    const testResult = await remoteControlApi.testConnection({
      host: connectionForm.host,
      port: connectionForm.port,
      username: connectionForm.username,
      password: connectionForm.password
    });

    if (!testResult.data) {
      throw new Error("无法连接到目标主机");
    }

    updateProgress(50, "正在建立VNC连接...");

    // 建立VNC连接
    const connectResult = await remoteControlApi.connect({
      host: connectionForm.host,
      port: connectionForm.port,
      username: connectionForm.username,
      password: connectionForm.password
    });

    if (connectResult.data) {
      currentSession = connectResult.data;
      websocketUrl = connectResult.data.websocketUrl || `ws://${connectionForm.host}:${connectionForm.port + 1000}`;

      updateProgress(80, "正在初始化显示...");

      // 使用noVNC建立连接
      await initNoVNCConnection();

      updateProgress(100, "连接成功");
      connectionStartTime.value = new Date();

      // 显示成功通知
      ElNotification({
        title: "连接成功",
        message: `已成功连接到 ${connectionForm.host}:${connectionForm.port}`,
        type: "success",
        duration: 3000
      });
    } else {
      throw new Error("连接建立失败");
    }
  } catch (error: any) {
    console.error("VNC connection failed:", error);
    statusMessage.value = error.message || t("layout.header.remoteControl.connectFailed");
    statusType.value = "error";
    ElMessage.error(error.message || t("layout.header.remoteControl.connectError"));

    // 重置状态
    connectionProgress.value = 0;
    isConnected.value = false;
    vncReady.value = false;
  } finally {
    connecting.value = false;
  }
};

// 断开 VNC 连接
const disconnectVNC = async () => {
  try {
    const result = await ElMessageBox.confirm(
      t("layout.header.remoteControl.disconnectConfirm"),
      t("layout.header.remoteControl.warning"),
      {
        confirmButtonText: t("layout.header.remoteControl.confirm"),
        cancelButtonText: t("layout.header.remoteControl.cancel"),
        type: "warning"
      }
    );

    if (result === "confirm") {
      // 断开noVNC连接
      if (vncClient) {
        vncClient.disconnect();
        vncClient = null;
      }

      // 通知后端断开连接
      if (currentSession) {
        await remoteControlApi.disconnect(currentSession.sessionId);
        currentSession = null;
      }

      // 清理显示区域
      if (vncDisplay.value) {
        vncDisplay.value.innerHTML = "";
      }

      isConnected.value = false;
      vncReady.value = false;
      statusMessage.value = t("layout.header.remoteControl.disconnected");
      statusType.value = "info";

      ElMessage.success(t("layout.header.remoteControl.disconnectSuccess"));
    }
  } catch (error: any) {
    console.error("Disconnect error:", error);
    ElMessage.error("断开连接时发生错误");
  }
};

// 发送 Ctrl+Alt+Del
const sendCtrlAltDel = async () => {
  if (vncClient) {
    try {
      // 使用noVNC发送Ctrl+Alt+Del
      vncClient.sendCtrlAltDel();
      ElMessage.success(t("layout.header.remoteControl.ctrlAltDelSent"));
    } catch (error: any) {
      console.error("Send Ctrl+Alt+Del error:", error);
      ElMessage.error("发送Ctrl+Alt+Del时发生错误");
    }
  }
};

// 发送 Alt+Tab
const sendAltTab = async () => {
  if (currentSession) {
    try {
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 18, true); // Alt down
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 9, true); // Tab down
      await new Promise(resolve => setTimeout(resolve, 100));
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 9, false); // Tab up
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 18, false); // Alt up
      ElMessage.success("已发送 Alt+Tab");
    } catch (error: any) {
      console.error("Send Alt+Tab error:", error);
      ElMessage.error("发送Alt+Tab时发生错误");
    }
  }
};

// 发送 Win 键
const sendWinKey = async () => {
  if (currentSession) {
    try {
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 91, true); // Win down
      await new Promise(resolve => setTimeout(resolve, 100));
      await remoteControlApi.sendKeyEvent(currentSession.sessionId, 91, false); // Win up
      ElMessage.success("已发送 Win 键");
    } catch (error: any) {
      console.error("Send Win key error:", error);
      ElMessage.error("发送Win键时发生错误");
    }
  }
};

// 刷新连接
const refreshConnection = async () => {
  if (!currentSession) return;

  refreshing.value = true;
  try {
    // 重新获取会话状态
    const statusResult = await remoteControlApi.getStatus(currentSession.sessionId);
    if (statusResult.data) {
      ElMessage.success("连接状态已刷新");
    }
  } catch (error: any) {
    console.error("Refresh connection error:", error);
    ElMessage.error("刷新连接时发生错误");
  } finally {
    refreshing.value = false;
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (vncDisplay.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
      isFullscreen.value = false;
    } else {
      vncDisplay.value.requestFullscreen();
      isFullscreen.value = true;
    }
  }
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 显示右键菜单
const showContextMenu = (event: MouseEvent) => {
  event.preventDefault();
  contextMenuVisible.value = true;
  contextMenuStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`
  };

  // 点击其他地方隐藏菜单
  const hideMenu = () => {
    contextMenuVisible.value = false;
    document.removeEventListener("click", hideMenu);
  };
  setTimeout(() => document.addEventListener("click", hideMenu), 0);
};

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText();
    if (currentSession) {
      // 这里应该实现将剪贴板内容发送到远程桌面
      ElMessage.success("已复制到远程剪贴板");
    }
  } catch (error) {
    ElMessage.error("复制失败");
  }
  contextMenuVisible.value = false;
};

// 从剪贴板粘贴
const pasteFromClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText();
    if (currentSession && text) {
      // 这里应该实现将文本输入到远程桌面
      ElMessage.success("已粘贴到远程桌面");
    }
  } catch (error) {
    ElMessage.error("粘贴失败");
  }
  contextMenuVisible.value = false;
};

// 显示设置
const showSettings = () => {
  ElMessage.info("设置功能开发中...");
  contextMenuVisible.value = false;
};

// 对话框关闭处理
const handleDialogClose = () => {
  if (isConnected.value) {
    ElMessageBox.confirm("当前有活动的VNC连接，关闭对话框将断开连接。是否继续？", "确认关闭", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        disconnectVNC();
      })
      .catch(() => {
        dialogVisible.value = true; // 取消关闭
      });
  }
};

// 截图
const takeScreenshot = async () => {
  if (vncClient) {
    try {
      // 从noVNC canvas获取截图
      const canvas = vncDisplay.value?.querySelector("canvas");
      if (canvas) {
        const dataURL = canvas.toDataURL("image/png");

        // 创建下载链接
        const link = document.createElement("a");
        link.href = dataURL;
        link.download = `vnc_screenshot_${new Date().getTime()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        ElMessage.success(t("layout.header.remoteControl.screenshotTaken"));
      } else {
        ElMessage.error("无法获取画面");
      }
    } catch (error: any) {
      console.error("Screenshot error:", error);
      ElMessage.error("截图时发生错误");
    }
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  connectionForm.host = "*************";
  connectionForm.port = 5900;
  connectionForm.username = "";
  connectionForm.password = "";
};

// VNC 事件处理
const onVNCDisconnected = () => {
  isConnected.value = false;
  vncReady.value = false;
  statusMessage.value = t("layout.header.remoteControl.disconnected");
  statusType.value = "warning";
};

const onCredentialsRequired = () => {
  ElMessage.warning(t("layout.header.remoteControl.credentialsRequired"));
};

// 初始化noVNC连接
const initNoVNCConnection = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      if (!vncDisplay.value) {
        reject(new Error("VNC显示区域未找到"));
        return;
      }

      // 清空显示区域
      vncDisplay.value.innerHTML = "";

      // 创建noVNC连接
      const RFBClass = window.RFB;
      if (!RFBClass) {
        throw new Error("noVNC library not loaded");
      }

      vncClient = new RFBClass(vncDisplay.value, websocketUrl, {
        credentials: {
          username: connectionForm.username,
          password: connectionForm.password
        }
      });

      // 设置noVNC事件监听器
      vncClient.addEventListener("connect", () => {
        console.log("noVNC connected");
        isConnected.value = true;
        vncReady.value = true;
        statusMessage.value = t("layout.header.remoteControl.connected");
        statusType.value = "success";
        ElMessage.success(t("layout.header.remoteControl.connectSuccess"));
        resolve();
      });

      vncClient.addEventListener("disconnect", (e: any) => {
        console.log("noVNC disconnected:", e.detail);
        onVNCDisconnected();
      });

      vncClient.addEventListener("credentialsrequired", () => {
        console.log("noVNC credentials required");
        onCredentialsRequired();
      });

      vncClient.addEventListener("securityfailure", (e: any) => {
        console.error("noVNC security failure:", e.detail);
        statusMessage.value = "安全验证失败: " + e.detail.reason;
        statusType.value = "error";
        ElMessage.error("安全验证失败: " + e.detail.reason);
        reject(new Error("安全验证失败"));
      });

      // 设置连接超时
      setTimeout(() => {
        if (!vncReady.value) {
          reject(new Error("连接超时"));
        }
      }, 15000); // 15秒超时

      // 设置noVNC选项
      vncClient.scaleViewport = true; // 自动缩放
      vncClient.resizeSession = false; // 不调整远程会话大小
      vncClient.showDotCursor = true; // 显示光标
    } catch (error: any) {
      console.error("Init noVNC error:", error);
      reject(error);
    }
  });
};

// 组件挂载时的初始化
onMounted(() => {
  // 监听全屏状态变化
  document.addEventListener("fullscreenchange", handleFullscreenChange);

  // 监听键盘事件
  document.addEventListener("keydown", handleGlobalKeydown);
});

// 全局键盘事件处理
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // 如果对话框打开且VNC已连接，处理一些全局快捷键
  if (dialogVisible.value && vncReady.value) {
    // F11 切换全屏
    if (event.key === "F11") {
      event.preventDefault();
      toggleFullscreen();
    }
    // Esc 退出全屏
    else if (event.key === "Escape" && isFullscreen.value) {
      event.preventDefault();
      toggleFullscreen();
    }
  }
};

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理VNC连接
  if (vncClient) {
    vncClient.disconnect();
    vncClient = null;
  }

  // 清理定时器
  stopPerformanceMonitoring();
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  // 清理事件监听器
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener("keydown", handleGlobalKeydown);

  // 清理会话
  if (currentSession) {
    remoteControlApi.disconnect(currentSession.sessionId).catch(console.error);
  }
});

defineExpose({ openDialog });
</script>

<style lang="scss">
.remote-control-main {
  .el-dialog__body {
    padding: 20px;
    background: #fafbfc;
    [class="dark"] & {
      background: #23272e;
    }
  }

  .remote-control-container {
    .config-card {
      margin-bottom: 20px;
      border-radius: 12px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          padding-left: 10px;
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          border-left: 4px solid var(--el-color-primary);
          [class="dark"] & {
            color: #8cd2ff;
            border-left: 4px solid #8cd2ff;
          }
        }
      }
    }

    .vnc-container {
      .vnc-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        margin-bottom: 10px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .toolbar-right {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .connection-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-tag {
            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .performance-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 10px;
        background: var(--el-color-info-light-9);
        border: 1px solid var(--el-color-info-light-7);
        border-radius: 6px;
        font-size: 12px;
        color: var(--el-color-info);

        .performance-item {
          display: flex;
          align-items: center;

          span {
            font-weight: 500;
          }
        }
      }

      .vnc-display {
        position: relative;
        width: 100%;
        height: 500px;
        background: #000;
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        overflow: hidden;
        cursor: crosshair;

        // noVNC样式
        :deep(.noVNC_canvas) {
          width: 100% !important;
          height: 100% !important;
        }

        :deep(.noVNC_screen) {
          width: 100% !important;
          height: 100% !important;
        }

        .vnc-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          color: #fff;

          .loading-icon {
            font-size: 48px;
            margin-bottom: 16px;
            animation: rotate 2s linear infinite;
          }

          p {
            font-size: 16px;
            margin: 0 0 16px 0;
          }
        }

        .context-menu {
          position: fixed;
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 9999;
          min-width: 120px;
          padding: 4px 0;

          .menu-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: var(--el-text-color-primary);
            transition: background-color 0.2s;

            &:hover {
              background: var(--el-color-primary-light-9);
              color: var(--el-color-primary);
            }
          }

          .menu-divider {
            height: 1px;
            background: var(--el-border-color);
            margin: 4px 0;
          }
        }
      }
    }

    .status-info {
      margin-top: 16px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 全屏样式
.vnc-display:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  border: none !important;
  border-radius: 0 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .remote-control-main {
    width: 95vw !important;

    .vnc-container {
      .vnc-toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;

        .toolbar-left,
        .toolbar-right {
          justify-content: center;
          flex-wrap: wrap;
        }

        .el-button-group {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          justify-content: center;
        }
      }

      .performance-bar {
        flex-direction: column;
        gap: 8px;
        text-align: center;

        .performance-item {
          justify-content: center;
        }
      }

      .vnc-display {
        height: 300px;
      }
    }

    .config-card {
      .card-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
    }
  }
}

@media (max-width: 480px) {
  .remote-control-main {
    width: 100vw !important;
    margin: 0 !important;
    border-radius: 0 !important;

    .vnc-container {
      .vnc-display {
        height: 250px;
      }
    }
  }
}
</style>
