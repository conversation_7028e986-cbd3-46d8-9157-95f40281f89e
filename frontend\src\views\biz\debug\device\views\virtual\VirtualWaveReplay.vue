<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getWaveReplayList"
      :init-param="initParam"
      :pagination="true"
      :data-callback="dataCallback"
      row-key="fileName"
      table-key="virtualWaveReplay"
      :max-height="'calc(100vh - 280px)'"
      :immediate="true"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-button type="primary" plain :icon="Refresh" @click="handleRefreshFiles">
            {{ t("device.virtualWaveReplay.getFiles") }}
          </el-button>
          <el-button type="primary" :icon="Upload" @click="handleAddFiles">
            {{ t("device.virtualWaveReplay.addFiles") }}
          </el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="VideoPlay" @click="playbackFaultRecord(scope.row)">
          {{ t("device.virtualWaveReplay.faultReplay") }}
        </el-button>
      </template>
    </ProTable>
  </div>

  <!-- 文件选择对话框 -->
  <CustomFileSelector ref="fileSelectorRef" :multiple="true" :file-types="['.cfg', '.dat', '.hdr', '.inf']" @confirm="handleFilesSelected" />

  <ProgressDialog ref="progressDialog" @cancel="handleCancelDownload"></ProgressDialog>
</template>

<script setup lang="ts" name="VirtualWaveReplay">
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import { FileItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Upload, VideoPlay, Refresh } from "@element-plus/icons-vue";
import { devicefileApi } from "@/api/modules/biz/debug/devicefile";
import { virtualDeviceApi } from "@/api/modules/biz/debug/virtualDevice";
import { MathUtils } from "@/utils/mathUtils";
import { ElMessageBox } from "element-plus";
import { createDebouncedSave, EventListenerManager } from "@/utils/performance";
import ProgressDialog from "../../dialog/ProgressDialog.vue";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { useDebugStore } from "@/stores/modules/debug";
import { IECNotify } from "@/api/interface";
import { ipc } from "@/api/request/ipcRenderer";

const { addConsole } = useDebugStore();

// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const fileSelectorRef = ref();

// 文件下载进度跟踪
const downloadProgress = ref({
  isDownloading: false,
  currentFile: "",
  totalFiles: 0,
  completedFiles: 0,
  currentFileProgress: 0,
  taskId: "", // 添加任务ID用于取消操作
  userCancelled: false // 标记用户是否主动取消
});

// 事件监听器管理
const eventManager = new EventListenerManager();

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ filePath: "/shr/wave_replay" });

// 默认使用 /shr/wave_replay 目录
const selectType = ref("/shr/wave_replay");

const { t } = useI18n();

// 刷新获取文件列表
const handleRefreshFiles = () => {
  console.log("[VirtualWaveReplay] 手动刷新文件列表");
  proTable.value?.getTableList();
};

// 添加录波回放文件
const handleAddFiles = () => {
  fileSelectorRef.value?.open();
};

// 处理文件选择完成
const handleFilesSelected = async (selectedData: any) => {
  // 处理单个文件或文件数组
  let selectedFiles: any[] = [];
  if (Array.isArray(selectedData)) {
    selectedFiles = selectedData;
  } else if (selectedData) {
    selectedFiles = [selectedData];
  }

  if (!selectedFiles || selectedFiles.length === 0) {
    return;
  }

  try {
    // 初始化下载进度
    downloadProgress.value = {
      isDownloading: true,
      currentFile: "",
      totalFiles: selectedFiles.length,
      completedFiles: 0,
      currentFileProgress: 0,
      taskId: "",
      userCancelled: false
    };

    progressDialog.value.show();
    progressDialog.value.setProgress(0, t("device.virtualWaveReplay.messages.preparingDownload"), true); // 初始状态使用不确定进度条
    progressDialog.value.setCancel(true); // 显示取消按钮
    progressDialog.value.setDetails("", 0, selectedFiles.length); // 显示详细进度

    // 构建文件下载参数 - 从FileItem对象中提取path属性
    const fileItems: any[] = [];

    for (let i = 0; i < selectedFiles.length; i++) {
      const fileItem = selectedFiles[i];
      // 提取文件路径，确保数据完全可序列化
      const filePath = fileItem.path || fileItem;
      fileItems.push({
        filePath: String(filePath),
        fileRow: i + 1
      });
    }

    // 创建纯净的参数对象
    const downloadParams = {
      remoteParentPath: String(selectType.value),
      fileItems: fileItems
    };

    console.log("下载参数:", JSON.stringify(downloadParams, null, 2));

    // 调用下载文件接口，将文件下载到装置的 /shr/wave_replay 目录
    const result = await devicefileApi.downloadDeviceFileByDevice(String(props.deviceId), downloadParams);

    if (Number(result.code) === 0) {
      console.log("[VirtualWaveReplay] 下载请求发送成功，等待后台进度通知");
      // 不在这里显示成功消息，等待 ALL_FILE_FINISH 状态通知
    } else {
      ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadFailed") + ": " + result.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error("文件下载失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
  } finally {
    // 重置下载状态
    downloadProgress.value.isDownloading = false;
    downloadProgress.value.userCancelled = false;
    progressDialog.value.setCancel(false); // 隐藏取消按钮
    // 如果没有通过事件自动隐藏，延迟隐藏进度条
    setTimeout(() => {
      if (progressDialog.value.progressDialog.show) {
        progressDialog.value.hide();
      }
    }, 1000);
  }
};

// 故障录波回放功能
const playbackFaultRecord = async (row: FileItem) => {
  console.log("playbackFaultRecord", row);

  if (String(row.fileName).endsWith("\\")) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.invalidFile"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  if (row.fileSize === 0) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.fileSizeZero", { fileName: row.fileName }), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  try {
    progressDialog.value.show();

    // 调用后台接口实现故障回放
    // 这里调用虚拟装置的故障回放接口
    const result = await virtualDeviceApi.playbackWaveReplayByDevice(props.deviceId, {
      fileName: row.fileName,
      filePath: selectType.value + "/" + row.fileName,
      fileSize: row.fileSize
    });

    if (Number(result.code) === 0) {
      ElMessageBox.alert(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }), t("device.virtualParam.success"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "success"
      });
      addConsole(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }));
    } else {
      ElMessageBox.alert(t("device.virtualWaveReplay.errors.playbackFailed") + ": " + result.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error("故障录波回放失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.playbackFailed"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
  } finally {
    progressDialog.value.hide();
  }
};

// 取消下载处理函数
const handleCancelDownload = async (): Promise<void> => {
  try {
    console.log("[VirtualWaveReplay] 取消下载请求 - isDownloading:", downloadProgress.value.isDownloading, "taskId:", downloadProgress.value.taskId);

    if (!downloadProgress.value.isDownloading) {
      console.log("[VirtualWaveReplay] 没有正在进行的下载任务");
      return;
    }

    if (!downloadProgress.value.taskId) {
      console.log("[VirtualWaveReplay] 没有找到任务ID");
      ElMessageBox.alert("没有找到任务ID，无法取消下载", t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
      return;
    }

    console.log("[VirtualWaveReplay] 正在取消下载任务:", downloadProgress.value.taskId);

    // 先标记为用户取消，防止后续ERROR状态触发额外弹窗
    downloadProgress.value.userCancelled = true;

    // 调用取消下载接口
    const result = await devicefileApi.cancelDownloadDeviceFileByDevice(String(props.deviceId), {
      taskids: [downloadProgress.value.taskId]
    });

    console.log("[VirtualWaveReplay] 取消下载响应:", result);

    if (Number(result.code) === 0) {
      // 重置下载状态
      downloadProgress.value.isDownloading = false;
      downloadProgress.value.taskId = "";
      downloadProgress.value.currentFile = "";
      downloadProgress.value.completedFiles = 0;
      downloadProgress.value.currentFileProgress = 0;

      // 隐藏取消按钮和进度对话框
      progressDialog.value.setCancel(false);
      progressDialog.value.hide();

      // ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadCancelled"), t("device.virtualParam.info"), {
      //   confirmButtonText: t("device.virtualParam.confirm"),
      //   type: "info"
      // });
      addConsole(t("device.virtualWaveReplay.messages.downloadCancelled"));
    } else {
      // 如果取消失败，重置用户取消标记
      downloadProgress.value.userCancelled = false;
      ElMessageBox.alert(t("device.virtualWaveReplay.messages.cancelFailed") + ": " + result.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error("取消下载失败:", error);
    // 如果取消失败，重置用户取消标记
    downloadProgress.value.userCancelled = false;
    ElMessageBox.alert(t("device.virtualWaveReplay.messages.cancelFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
  }
};

// 文件下载进度事件监听
const handleDownloadNotify = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  if (notify.type === "fileDownload") {
    const notifyData = notify.data as any;
    const status = notifyData.status;
    const progress = notifyData.progress;
    const fileItem = notifyData.fileItem;
    const errorMsg = notifyData.errorMsg;
    const taskId = notifyData.taskid;

    if (!downloadProgress.value.isDownloading) return;

    // 保存任务ID用于取消操作
    if (taskId) {
      downloadProgress.value.taskId = taskId;
      console.log("[VirtualWaveReplay] 获取到任务ID:", taskId);
    }

    let progressText = "";
    let percentage = 0;

    if (status === "CHECK_FILE_INFO") {
      progressText = t("device.virtualWaveReplay.messages.checkingFileInfo");
      percentage = 10;
      // 更新进度条，使用不确定状态
      progressDialog.value.setProgress(percentage, progressText, true);
      return;
    } else if (status === "INIT") {
      progressText = t("device.virtualWaveReplay.messages.initializingDownload");
      percentage = 15;
      // 更新进度条，使用不确定状态
      progressDialog.value.setProgress(percentage, progressText, true);
      return;
    } else if (status === "DATA_TRANSFER") {
      const fileName = fileItem?.filePath ? fileItem.filePath.split(/[\/\\]/).pop() : "";
      downloadProgress.value.currentFile = fileName;
      downloadProgress.value.currentFileProgress = progress?.filePercentage || 0;

      // 计算总体进度
      const fileProgress = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      const currentFileContribution = (progress?.filePercentage || 0) / downloadProgress.value.totalFiles;
      percentage = Math.min(95, fileProgress + currentFileContribution);

      progressText = fileName
        ? t("device.virtualWaveReplay.messages.downloadingFile", {
            fileName,
            current: downloadProgress.value.completedFiles + 1,
            total: downloadProgress.value.totalFiles
          })
        : t("device.virtualWaveReplay.messages.downloading");

      // 更新详细进度信息 - 当前正在处理的文件序号是 completedFiles + 1
      const currentFileIndex = downloadProgress.value.completedFiles + 1;
      console.log(
        `[VirtualWaveReplay] DATA_TRANSFER - 当前文件: ${fileName}, 文件序号: ${currentFileIndex}/${downloadProgress.value.totalFiles}, 文件進度: ${progress?.filePercentage}%`
      );
      progressDialog.value.setDetails(fileName, currentFileIndex, downloadProgress.value.totalFiles, progress?.filePercentage);

      // 更新进度条，使用确定的进度条显示实际百分比
      progressDialog.value.setProgress(percentage, progressText, false);
      return;
    } else if (status === "SINGLE_FILE_FINISH") {
      downloadProgress.value.completedFiles++;
      downloadProgress.value.currentFileProgress = 100;

      percentage = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      progressText = t("device.virtualWaveReplay.messages.fileCompleted", {
        current: downloadProgress.value.completedFiles,
        total: downloadProgress.value.totalFiles
      });

      // 更新详细进度信息 - 显示刚完成的文件
      console.log(
        `[VirtualWaveReplay] SINGLE_FILE_FINISH - 完成文件: ${downloadProgress.value.currentFile}, 已完成: ${downloadProgress.value.completedFiles}/${downloadProgress.value.totalFiles}`
      );
      progressDialog.value.setDetails(
        downloadProgress.value.currentFile,
        downloadProgress.value.completedFiles,
        downloadProgress.value.totalFiles,
        100
      );

      // 更新进度条，使用确定的进度条显示实际百分比
      progressDialog.value.setProgress(percentage, progressText, false);
      return;
    } else if (status === "ALL_FILE_FINISH") {
      downloadProgress.value.completedFiles = downloadProgress.value.totalFiles;
      downloadProgress.value.isDownloading = false;

      percentage = 100;
      progressText = t("device.virtualWaveReplay.messages.downloadSuccess");

      // 隐藏取消按钮
      progressDialog.value.setCancel(false);
      progressDialog.value.setDetails("", downloadProgress.value.totalFiles, downloadProgress.value.totalFiles, 100);

      // 更新进度条，显示100%完成
      progressDialog.value.setProgress(percentage, progressText, false);

      // 延迟隐藏进度条并刷新表格
      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadSuccess"), t("device.virtualParam.success"), {
          confirmButtonText: t("device.virtualParam.confirm"),
          type: "success"
        });
        addConsole(t("device.virtualWaveReplay.messages.downloadSuccess"));
        proTable.value?.refresh();
      }, 500);
      return;
    } else if (status === "ERROR") {
      downloadProgress.value.isDownloading = false;

      // 如果是用户取消，不显示错误消息
      if (downloadProgress.value.userCancelled) {
        console.log("[VirtualWaveReplay] 用户取消下载，忽略ERROR状态");
        return;
      }

      progressText = t("device.virtualWaveReplay.messages.downloadError") + (errorMsg ? ": " + errorMsg : "");
      percentage = downloadProgress.value.completedFiles > 0 ? (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100 : 0;

      // 隐藏取消按钮
      progressDialog.value.setCancel(false);

      // 更新进度条显示错误状态
      progressDialog.value.setProgress(percentage, progressText, false);

      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(
          t("device.virtualWaveReplay.messages.downloadFailed") + (errorMsg ? ": " + errorMsg : ""),
          t("device.virtualParam.error"),
          {
            confirmButtonText: t("device.virtualParam.confirm"),
            type: "error"
          }
        );
      }, 500);
      return;
    }
  }
};

// 处理返回的表格数据
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  // hideLoading();
  try {
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }

    // 直接返回数据
    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  }
};

// 获取文件列表 - 参考 VirtualDigitalInput.vue 的简单获取方式
const getWaveReplayList = async (params: any) => {
  console.log("[VirtualWaveReplay] getWaveReplayList 开始获取数据，参数:", params);

  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.filePath = selectType.value;
  delete newParams.createTime;

  // 添加分页参数
  newParams.pageNum = params.pageNum || 1;
  newParams.pageSize = params.pageSize || 10;

  console.log("[VirtualWaveReplay] 实际请求参数:", newParams);

  try {
    // 使用分页查询接口
    const result = await devicefileApi.getDeviceFilePageByDevice(props.deviceId, newParams);
    console.log("[VirtualWaveReplay] 后台响应:", result);

    if (Number(result.code) === 0 && result.data) {
      const data = result.data as { list: any[]; total: number; pageNum: number; pageSize: number };
      const items = Array.isArray(data.list) ? data.list : [];

      // 格式化文件大小
      items.forEach((item: any) => {
        if (item.fileSize && typeof item.fileSize === "number") {
          item.fileSizeAs = MathUtils.formatBytes(item.fileSize);
        } else {
          item.fileSizeAs = "--";
        }
      });

      console.log("[VirtualWaveReplay] 处理后的文件列表:", items);

      // 参考 VirtualDigitalInput.vue，直接返回处理后的数据结构
      return result || { list: [], total: 0 };
    } else {
      console.error("[VirtualWaveReplay] 接口返回错误:", result.msg);
      ElMessageBox.alert(
        t("device.virtualWaveReplay.errors.getFilesFailed") + ": " + (result.msg || "Unknown error"),
        t("device.virtualParam.error"),
        {
          confirmButtonText: t("device.virtualParam.confirm"),
          type: "error"
        }
      );
      return { list: [], total: 0 };
    }
  } catch (error) {
    console.error("[VirtualWaveReplay] 获取文件列表失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.getFilesFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
    return { list: [], total: 0 };
  }
};

// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "index", label: t("device.virtualWaveReplay.serialNumber"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("device.virtualWaveReplay.fileName"),
    sortable: true,
    search: { el: "input" },
    render: (scope: any) => {
      // 调试输出查看字段
      console.log("[VirtualWaveReplay] fileName 字段:", scope.row);
      return scope.row.fileName || "--";
    }
  },
  {
    prop: "fileSize",
    label: t("device.virtualWaveReplay.fileSize"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // a、b 是整行数据，这里按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: (scope: any) => {
      // 显示格式化后的文件大小
      return scope.row.fileSizeAs || MathUtils.formatBytes(scope.row.fileSize) || "--";
    }
  },
  {
    prop: "lastModified",
    label: t("device.virtualWaveReplay.lastModified"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified || 0).getTime() || 0;
      const timeB = new Date(b.lastModified || 0).getTime() || 0;
      return timeA - timeB;
    },
    render: (scope: any) => {
      return scope.row.lastModified || "--";
    }
  },
  { prop: "operation", label: t("device.virtualWaveReplay.operation"), fixed: "right", width: 250 }
]);

// 删除了不需要的函数，现在使用ProTable的内置功能

// 使用性能优化的保存函数
const debouncedSaveData = createDebouncedSave(props.deviceId + ".waveReplayData", 1000);

onMounted(() => {
  console.log("[VirtualWaveReplay] 组件已挂载，装置ID:", props.deviceId);
  console.log("[VirtualWaveReplay] 初始化参数:", initParam);
  console.log("[VirtualWaveReplay] 选择的目录:", selectType.value);

  addAllListeners();
  // 添加文件下载进度监听
  ipc.on("filedownload_notify", handleDownloadNotify);

  // 手动触发一次数据加载，确保组件初始化时有数据
  setTimeout(() => {
    console.log("[VirtualWaveReplay] 手动触发数据加载");
    proTable.value?.getTableList();
  }, 500);
});

onBeforeUnmount(() => {
  // 立即保存一次数据
  debouncedSaveData.flush();
  removeAllListeners();
  // 移除文件下载进度监听
  ipc.removeAllListeners("filedownload_notify");
});

function removeAllListeners() {
  eventManager.cleanup();
}

function addAllListeners() {
  const saveHandler = () => debouncedSaveData.flush();
  window.addEventListener("beforeunload", saveHandler);
  eventManager.add("beforeunload", saveHandler);
}
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
