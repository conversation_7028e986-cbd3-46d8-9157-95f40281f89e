"use strict";

import { remoteControlService } from "../../service/remoteControl/remoteControlService";
import { logger } from "ee-core/log";
import {
  ERROR_CODES,
  handleErrorResponse,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";

/**
 * 远程控制控制器
 * 处理VNC远程桌面相关的请求
 * <AUTHOR>
 * @class
 */
class RemoteControlController {
  /**
   * 测试VNC连接
   * testConnection
   */
  async testConnection(connectionConfig: {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }): Promise<ApiResponse> {
    try {
      logger.info(
        "[RemoteControlController] testConnection - Start",
        connectionConfig
      );

      if (!connectionConfig.host || !connectionConfig.port) {
        logger.warn(
          "[RemoteControlController] testConnection - Invalid parameters"
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "主机地址和端口不能为空",
          false
        );
      }

      const result =
        await remoteControlService.testConnection(connectionConfig);
      logger.info(
        `[RemoteControlController] testConnection - Success, 连接测试结果: ${result}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        result ? "连接测试成功" : "连接测试失败",
        result
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] testConnection - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 建立VNC连接
   * connect
   */
  async connect(connectionConfig: {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }): Promise<ApiResponse> {
    try {
      logger.info(
        "[RemoteControlController] connect - Start",
        connectionConfig
      );

      if (!connectionConfig.host || !connectionConfig.port) {
        logger.warn("[RemoteControlController] connect - Invalid parameters");
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "主机地址和端口不能为空",
          null
        );
      }

      const session = await remoteControlService.connect(connectionConfig);
      logger.info(
        `[RemoteControlController] connect - Success, 会话ID: ${session.sessionId}`
      );

      return handleCustomResponse(ERROR_CODES.SUCCESS, "VNC连接建立成功", {
        sessionId: session.sessionId,
        host: session.host,
        port: session.port,
        status: session.status,
        createdAt: session.createdAt,
        websocketUrl: session.websocketUrl,
      });
    } catch (error) {
      logger.error("[RemoteControlController] connect - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 断开VNC连接
   * disconnect
   */
  async disconnect(sessionId: string): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] disconnect - Start, 会话ID: ${sessionId}`
      );

      if (!sessionId) {
        logger.warn("[RemoteControlController] disconnect - Invalid sessionId");
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "会话ID不能为空",
          false
        );
      }

      const result = await remoteControlService.disconnect(sessionId);
      logger.info(
        `[RemoteControlController] disconnect - Success, 断开结果: ${result}`
      );

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        result ? "断开连接成功" : "断开连接失败",
        result
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] disconnect - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 获取连接状态
   * getStatus
   */
  async getStatus(sessionId?: string): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] getStatus - Start, 会话ID: ${sessionId}`
      );

      const status = await remoteControlService.getStatus(sessionId);
      logger.info("[RemoteControlController] getStatus - Success");

      return handleCustomResponse(ERROR_CODES.SUCCESS, "获取状态成功", status);
    } catch (error) {
      logger.error(
        "[RemoteControlController] getStatus - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 发送键盘事件
   * sendKeyEvent
   */
  async sendKeyEvent(
    sessionId: string,
    keyCode: number,
    pressed: boolean
  ): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] sendKeyEvent - Start, 会话ID: ${sessionId}, 键码: ${keyCode}, 按下: ${pressed}`
      );

      if (!sessionId || keyCode === undefined || pressed === undefined) {
        logger.warn(
          "[RemoteControlController] sendKeyEvent - Invalid parameters"
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "参数不完整",
          false
        );
      }

      const result = await remoteControlService.sendKeyEvent(
        sessionId,
        keyCode,
        pressed
      );
      logger.info(
        `[RemoteControlController] sendKeyEvent - Success, 发送结果: ${result}`
      );

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        result ? "键盘事件发送成功" : "键盘事件发送失败",
        result
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] sendKeyEvent - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 发送鼠标事件
   * sendMouseEvent
   */
  async sendMouseEvent(
    sessionId: string,
    x: number,
    y: number,
    buttonMask: number
  ): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] sendMouseEvent - Start, 会话ID: ${sessionId}, 坐标: (${x}, ${y}), 按钮: ${buttonMask}`
      );

      if (
        !sessionId ||
        x === undefined ||
        y === undefined ||
        buttonMask === undefined
      ) {
        logger.warn(
          "[RemoteControlController] sendMouseEvent - Invalid parameters"
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "参数不完整",
          false
        );
      }

      const result = await remoteControlService.sendMouseEvent(
        sessionId,
        x,
        y,
        buttonMask
      );
      logger.info(
        `[RemoteControlController] sendMouseEvent - Success, 发送结果: ${result}`
      );

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        result ? "鼠标事件发送成功" : "鼠标事件发送失败",
        result
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] sendMouseEvent - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 发送Ctrl+Alt+Del
   * sendCtrlAltDel
   */
  async sendCtrlAltDel(sessionId: string): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] sendCtrlAltDel - Start, 会话ID: ${sessionId}`
      );

      if (!sessionId) {
        logger.warn(
          "[RemoteControlController] sendCtrlAltDel - Invalid sessionId"
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "会话ID不能为空",
          false
        );
      }

      const result = await remoteControlService.sendCtrlAltDel(sessionId);
      logger.info(
        `[RemoteControlController] sendCtrlAltDel - Success, 发送结果: ${result}`
      );

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        result ? "Ctrl+Alt+Del发送成功" : "Ctrl+Alt+Del发送失败",
        result
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] sendCtrlAltDel - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 获取屏幕截图
   * getScreenshot
   */
  async getScreenshot(sessionId: string): Promise<ApiResponse> {
    try {
      logger.info(
        `[RemoteControlController] getScreenshot - Start, 会话ID: ${sessionId}`
      );

      if (!sessionId) {
        logger.warn(
          "[RemoteControlController] getScreenshot - Invalid sessionId"
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          "会话ID不能为空",
          null
        );
      }

      const screenshot = await remoteControlService.getScreenshot(sessionId);
      logger.info("[RemoteControlController] getScreenshot - Success");

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        "获取截图成功",
        screenshot
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] getScreenshot - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 获取VNC会话列表
   * getSessions
   */
  async getSessions(): Promise<ApiResponse> {
    try {
      logger.info("[RemoteControlController] getSessions - Start");

      const sessions = await remoteControlService.getSessions();
      logger.info(
        `[RemoteControlController] getSessions - Success, 会话数量: ${sessions.length}`
      );

      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        "获取会话列表成功",
        sessions
      );
    } catch (error) {
      logger.error(
        "[RemoteControlController] getSessions - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }
}

RemoteControlController.toString = () => "[class RemoteControlController]";
export default RemoteControlController;
