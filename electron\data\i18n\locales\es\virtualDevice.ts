/**
 * Virtual device Spanish language pack
 */
export default {
  menus: {
    analog: {
      name: "Analógico",
      desc: "Parámetros analógicos",
    },
    digitalInput: {
      name: "Entrada Digital",
      desc: "Parámetros de entrada digital",
    },
    digitalOutput: {
      name: "Salida Digital",
      desc: "Parámetros de salida digital",
    },
    fault: {
      name: "Falla",
      desc: "Parámetros de falla",
    },
    led: {
      name: "Parámetros LED",
      desc: "Parámetros LED",
    },
    waveReplay: {
      name: "Reproducción de Falla",
      desc: "Reproducción de forma de onda de falla",
    },
  },
  items: {
    analog: {
      name: "Entrada Analógica 1",
      desc: "Entrada Analógica 1",
    },
    digitalInput: {
      name: "Entrada Digital 1",
      desc: "Entrada Digital 1",
    },
    digitalOutput: {
      name: "Salida Digital 1",
      desc: "Salida Digital 1",
    },
    fault: {
      name: "Señal de Falla 1",
      desc: "Señal de Falla 1",
    },
    led: {
      name: "Brillo LED",
      desc: "Brillo LED",
    },
    waveReplay: {
      name: "Archivo de Forma de Onda",
      desc: "Archivo de Forma de Onda",
    },
  },
  // Relacionado con importación/exportación
  importExport: {
    export: {
      success: "Exportación exitosa",
      failed: "Exportación fallida",
      noData: "No hay datos para exportar",
      invalidPath: "Ruta de archivo inválida",
    },
    import: {
      success: "Importación exitosa",
      failed: "Importación fallida",
      parsing: "Analizando archivo Excel...",
      validating: "Validando datos...",
      updating: "Actualizando parámetros...",
    },
    template: {
      export: "Exportar Plantilla",
      name: "Plantilla de Importación",
    },
  },
  // Encabezados de columnas Excel
  columns: {
    index: "Índice",
    name: "Nombre",
    description: "Descripción",
    dataValue: "Valor",
    ang: "Ángulo",
  },
  // Mensajes de error de validación
  validation: {
    required: {
      cmdType: "El tipo de comando no puede estar vacío",
      filePath: "La ruta del archivo no puede estar vacía",
      name: "Fila {row}: El campo nombre no puede estar vacío",
    },
    format: {
      fileExtension: "Solo se admiten archivos Excel en formato .xlsx",
      dataValue: "Fila {row}: El valor de datos debe ser un número válido",
      ang: "Fila {row}: El ángulo debe ser un número válido",
    },
    range: {
      angValue:
        "Fila {row}: El valor del ángulo debe estar entre -360 y 360 grados",
    },
    file: {
      notExists: "El archivo no existe: {filePath}",
      noValidData: "No hay datos válidos en el archivo Excel",
      noValidRows: "No hay datos válidos para importar",
      parseError: "Error al analizar el archivo Excel: {error}",
      exportError: "Error al exportar el archivo Excel: {error}",
    },
    data: {
      validationFailed: "Validación de datos fallida:\n{errors}",
      updateFailed:
        "Los datos de importación se analizaron correctamente, pero la actualización falló",
    },
  },
  // Mensajes de operación
  messages: {
    exportStart: "Iniciando exportación de parámetros del dispositivo virtual",
    exportSuccess:
      "Parámetros del dispositivo virtual exportados exitosamente: {filePath}, cantidad de datos: {count}",
    importStart: "Iniciando importación de parámetros del dispositivo virtual",
    importSuccess:
      "Parámetros del dispositivo virtual importados y actualizados exitosamente: {filePath}, cantidad actualizada: {count}",
    parseSuccess:
      "Archivo Excel analizado exitosamente, cantidad de datos: {count}",
    validDataCount: "Cantidad de datos válidos: {count}",
    templateExportSuccess:
      "Plantilla de importación exportada exitosamente: {filePath}",
  },
};