<template>
  <div class="operate-container card">
    <div class="button-container">
      <el-button
        :icon="BrushFilled"
        plain
        @mouseenter="handleMouseEnter()"
        @mouseleave="handleMouseLeave()"
        type="primary"
        @click="manualWave()"
        >{{ t("device.operate.manualWave") }}</el-button
      >
      <el-button
        :icon="Flag"
        plain
        type="primary"
        @mouseenter="handleMouseEnter()"
        @mouseleave="handleMouseLeave()"
        @click="resetDevice"
        >{{ t("device.operate.resetDevice") }}</el-button
      >
      <el-button
        :icon="Delete"
        type="danger"
        @mouseenter="handleMouseEnter()"
        @mouseleave="handleMouseLeave()"
        plain
        @click="clearReport"
        >{{ t("device.operate.clearReport") }}</el-button
      >
      <el-button
        :icon="Delete"
        type="danger"
        @mouseenter="handleMouseEnter()"
        @mouseleave="handleMouseLeave()"
        plain
        @click="clearWave"
        >{{ t("device.operate.clearWave") }}</el-button
      >

      <div style="width: 100%; height: 30px; padding: 15px">
        <el-progress
          :duration="3"
          :percentage="progressDialog.percentage"
          :text-inside="false"
          striped
          v-show="progressDialog.show"
          :stroke-width="10"
          :indeterminate="progressDialog.indeterminate"
          style="margin-top: 40px"
        >
          <span>{{ t("device.operate.executing") }}</span>
        </el-progress>
      </div>
    </div>
    <div ref="img" class="not-container flx-center" tabindex="-1">
      <transition name="status-fade" mode="out-in">
        <div v-if="operateResult" key="result" class="status-wrapper" :class="statusClass">
          <img :src="imgSrc" class="not-img" alt="success" />
          <div class="not-detail">
            <h4 class="result-text">{{ operateResult }}</h4>
          </div>
        </div>
        <div v-else key="default" class="status-wrapper status-default">
          <img :src="imgSrc" class="not-img" alt="info" />
          <div class="not-detail">
            <h4 class="result-text">{{ t("device.operate.selectOperation") }}</h4>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>
<script setup lang="ts">
import { BrushFilled, Flag, Delete } from "@element-plus/icons-vue";
import { deviceoperationApi } from "@/api/modules/biz/debug/deviceoperation";
import { useDebugStore } from "@/stores/modules/debug";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const progressDialog = ref({
  show: false,
  percentage: 75,
  progressText: t("device.operate.executing"),
  indeterminate: true
});
const { addConsole } = useDebugStore();
const props = defineProps<{ deviceId: string }>();
const operateResult = ref("");
const imgSrc = ref();
const img = ref();
const isTriggered = ref(false);
const currentStatus = ref(""); // 新增状态标识

imgSrc.value = new URL("@/assets/debug/images/info.png", import.meta.url).href;

// 计算状态样式类
const statusClass = computed(() => {
  if (!operateResult.value) return "";
  if (currentStatus.value === "success") return "status-success";
  if (currentStatus.value === "error") return "status-error";
  return "";
});

const handleMouseEnter = () => {
  // 清除原来的状态和结果
  operateResult.value = "";
  currentStatus.value = "";
  imgSrc.value = new URL("@/assets/debug/images/info.png", import.meta.url).href;
  isTriggered.value = true;
};

const handleMouseLeave = () => {
  isTriggered.value = false;
};

const manualWave = async () => {
  progressDialog.value.show = true;
  setTimeout(async () => {
    const result = await deviceoperationApi.manualWaveByDevice(props.deviceId);
    console.log(result);
    if (Number(result.code) == 0) {
      addConsole(t("device.operate.success.manualWave"));
      operateResult.value = t("device.operate.success.manualWave");
      imgSrc.value = new URL("@/assets/debug/images/success.png", import.meta.url).href;
      currentStatus.value = "success";
    } else {
      operateResult.value = t("device.operate.fail.manualWave") + result.msg;
      addConsole(t("device.operate.fail.manualWave") + result.msg);
      imgSrc.value = new URL("@/assets/debug/images/fail.png", import.meta.url).href;
      currentStatus.value = "error";
    }
    progressDialog.value.show = false;
  }, 2000);
};

// 装置复归x
const resetDevice = () => {
  progressDialog.value.show = true;
  setTimeout(async () => {
    const result = await deviceoperationApi.resetDeviceByDevice(props.deviceId);
    console.log(result);
    if (Number(result.code) == 0) {
      addConsole(t("device.operate.success.resetDevice"));
      operateResult.value = t("device.operate.success.resetDevice");
      imgSrc.value = new URL("@/assets/debug/images/success.png", import.meta.url).href;
      currentStatus.value = "success";
    } else {
      operateResult.value = t("device.operate.fail.resetDevice") + result.msg;
      addConsole(t("device.operate.fail.resetDevice") + result.msg);
      imgSrc.value = new URL("@/assets/debug/images/fail.png", import.meta.url).href;
      currentStatus.value = "error";
    }
    progressDialog.value.show = false;
  }, 2000);
};
// 清除报告
const clearReport = () => {
  progressDialog.value.show = true;
  setTimeout(async () => {
    const result = await deviceoperationApi.clearReportByDevice(props.deviceId);
    console.log(result);
    if (Number(result.code) == 0) {
      addConsole(t("device.operate.success.clearReport"));
      operateResult.value = t("device.operate.success.clearReport");
      imgSrc.value = new URL("@/assets/debug/images/success.png", import.meta.url).href;
      currentStatus.value = "success";
    } else {
      operateResult.value = t("device.operate.fail.clearReport") + result.msg;
      addConsole(t("device.operate.fail.clearReport") + result.msg);
      imgSrc.value = new URL("@/assets/debug/images/fail.png", import.meta.url).href;
      currentStatus.value = "error";
    }
    progressDialog.value.show = false;
  }, 2000);
};

// 清除录波
const clearWave = () => {
  progressDialog.value.show = true;
  setTimeout(async () => {
    const result = await deviceoperationApi.clearWaveByDevice(props.deviceId);
    console.log(result);
    if (Number(result.code) == 0) {
      addConsole(t("device.operate.success.clearWave"));
      operateResult.value = t("device.operate.success.clearWave");
      imgSrc.value = new URL("@/assets/debug/images/success.png", import.meta.url).href;
      currentStatus.value = "success";
    } else {
      operateResult.value = t("device.operate.fail.clearWave") + result.msg;
      addConsole(t("device.operate.fail.clearWave") + result.msg);
      imgSrc.value = new URL("@/assets/debug/images/fail.png", import.meta.url).href;
      currentStatus.value = "error";
    }
    progressDialog.value.show = false;
  }, 2000);
};
</script>
<style lang="scss">
.operate-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  margin-top: 5px;
  margin-left: 0;
}
.button-container {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}
.not-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70%;
  height: 70%;
  .not-img {
    width: 200px;
    height: 200px;
    margin-right: 0;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 6px solid;
    border-radius: 24px;
    border-image: linear-gradient(135deg, #60a5fa 0%, #10b981 100%);
    border-image-slice: 1;
    box-shadow:
      0 8px 32px 0 rgb(56 189 248 / 18%),
      0 2px 8px 0 rgb(16 185 129 / 12%);
    transition:
      box-shadow 0.3s,
      border 0.3s,
      transform 0.3s;
    animation: img-pop 0.7s cubic-bezier(0.23, 1, 0.32, 1);
    &:hover {
      filter: none;
      border-image: linear-gradient(135deg, #60a5fa 0%, #38f9fa 50%, #34d399 100%);
      border-image-slice: 1;
      box-shadow:
        0 12px 40px 0 rgb(56 189 248 / 22%),
        0 4px 16px 0 rgb(16 185 129 / 16%),
        0 0 0 4px rgb(56 189 248 / 10%);
      animation: img-scale-up 0.4s cubic-bezier(0.36, 0.07, 0.19, 0.97) forwards;
    }
  }
  .not-detail {
    display: flex;
    flex-direction: column;
    h2,
    h4 {
      padding: 0;
      margin: 0;
    }
    h2 {
      font-size: 60px;
      color: var(--el-text-color-primary);
    }
    h4 {
      margin: 10px 0;
      font-size: 20px;
      font-weight: normal;
      color: var(--el-text-color-primary);
    }
    .el-button {
      width: 100px;
    }
  }
}

// 状态动画样式
.status-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 200px;
  min-height: 150px;
  padding: 32px;
  border-radius: 16px;
  .not-img {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
  .result-text {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
  }
}
.status-default {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #94a3b8;
  box-shadow: 0 4px 16px 0 rgb(148 163 184 / 15%);
  .result-text {
    color: #64748b;
  }
}
.status-success {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #10b981;
  box-shadow: 0 8px 32px 0 rgb(16 185 129 / 20%);
  animation: success-bounce 0.6s ease-out;
  .result-text {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.status-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 2px solid #ef4444;
  box-shadow: 0 8px 32px 0 rgb(239 68 68 / 20%);
  animation: error-shake 0.6s ease-out;
  .result-text {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

// 状态动画
@keyframes success-bounce {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  50% {
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes error-shake {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  25% {
    transform: translateX(8px);
  }
  50% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(2px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 状态淡入动画
.status-fade-enter-active {
  transition: all 0.6s ease-out;
}
.status-fade-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
}
.status-fade-enter-to {
  opacity: 1;
  transform: scale(1) translateY(0);
}

@keyframes img-pop {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(40px);
  }
  60% {
    opacity: 1;
    transform: scale(1.08) translateY(-8px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes img-scale-up {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.12);
  }
}
</style>
