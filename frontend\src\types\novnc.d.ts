// NoVNC 类型声明文件
declare module "@novnc/novnc/lib/rfb" {
  export default class RFB {
    constructor(target: HTMLElement, url: string, options?: RFBOptions);

    // 属性
    scaleViewport: boolean;
    resizeSession: boolean;
    showDotCursor: boolean;

    // 方法
    disconnect(): void;
    sendCtrlAltDel(): void;
    addEventListener(type: string, listener: (event: any) => void): void;
    removeEventListener(type: string, listener: (event: any) => void): void;

    // 静态方法
    static supportsCursorURIs(): boolean;
    static supportsFullscreen(): boolean;
  }

  interface RFBOptions {
    credentials?: {
      username?: string;
      password?: string;
    };
    repeaterID?: string;
    shared?: boolean;
    wsProtocols?: string[];
  }
}

// 全局 Window 接口扩展
declare global {
  interface Window {
    RFB: typeof import("@novnc/novnc/lib/rfb").default;
  }
}

export {};
