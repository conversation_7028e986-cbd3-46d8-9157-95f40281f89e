import { t } from "../data/i18n/i18n";

/**
 * 虚拟设备参数校验器
 * 统一前后端校验逻辑，确保一致性
 */
export class VirtualDeviceValidator {
  
  /**
   * 校验基本参数
   * @param cmdType 命令类型
   * @param filePath 文件路径（可选）
   * @returns 校验结果
   */
  static validateBasicParams(cmdType?: string, filePath?: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!cmdType || cmdType.trim() === '') {
      errors.push(t("virtualDevice.validation.required.cmdType"));
    }

    if (filePath !== undefined && (!filePath || filePath.trim() === '')) {
      errors.push(t("virtualDevice.validation.required.filePath"));
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 校验文件格式
   * @param filePath 文件路径
   * @returns 校验结果
   */
  static validateFileFormat(filePath: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!filePath || filePath.trim() === '') {
      errors.push(t("virtualDevice.validation.required.filePath"));
      return { isValid: false, errors };
    }

    const fileExt = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
    if (fileExt !== '.xlsx') {
      errors.push(t("virtualDevice.validation.format.fileExtension"));
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 校验单个参数项
   * @param item 参数项
   * @param index 行索引（用于错误提示）
   * @returns 校验结果
   */
  static validateParameterItem(item: any, index: number): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const rowNumber = index + 2; // Excel行号（考虑表头）

    // 校验名称字段（必填）
    if (!item.name || typeof item.name !== 'string' || item.name.trim() === '') {
      errors.push(t("virtualDevice.validation.required.name", { row: rowNumber }));
    }

    // 校验数据值（如果提供）
    if (item.dataValue !== undefined && item.dataValue !== null && item.dataValue !== '') {
      const dataValue = parseFloat(item.dataValue);
      if (isNaN(dataValue)) {
        errors.push(t("virtualDevice.validation.format.dataValue", { row: rowNumber }));
      }
    }

    // 校验相角（如果提供）
    if (item.ang !== undefined && item.ang !== null && item.ang !== '') {
      const ang = parseFloat(item.ang);
      if (isNaN(ang)) {
        errors.push(t("virtualDevice.validation.format.ang", { row: rowNumber }));
      } else if (ang < -360 || ang > 360) {
        errors.push(t("virtualDevice.validation.range.angValue", { row: rowNumber }));
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 校验批量参数数据
   * @param data 参数数据数组
   * @returns 校验结果和处理后的有效数据
   */
  static validateBatchData(data: any[]): { isValid: boolean; errors: string[]; validData: any[] } {
    const errors: string[] = [];
    const validData: any[] = [];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push(t("virtualDevice.validation.file.noValidData"));
      return { isValid: false, errors, validData };
    }

    data.forEach((item, index) => {
      const itemValidation = this.validateParameterItem(item, index);
      
      if (itemValidation.isValid) {
        // 处理有效数据，确保数据类型正确
        validData.push({
          name: item.name.trim(),
          dataValue: item.dataValue !== undefined && item.dataValue !== null && item.dataValue !== '' 
            ? parseFloat(item.dataValue) : undefined,
          ang: item.ang !== undefined && item.ang !== null && item.ang !== '' 
            ? parseFloat(item.ang) : undefined,
          description: item.description || ""
        });
      } else {
        errors.push(...itemValidation.errors);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      validData
    };
  }

  /**
   * 校验数值范围
   * @param value 数值
   * @param min 最小值
   * @param max 最大值
   * @param fieldName 字段名（用于错误消息）
   * @param rowNumber 行号（用于错误消息）
   * @returns 校验结果
   */
  static validateNumberRange(
    value: number, 
    min: number, 
    max: number, 
    fieldName: string, 
    rowNumber?: number
  ): { isValid: boolean; error?: string } {
    if (isNaN(value) || value < min || value > max) {
      const errorKey = fieldName === 'ang' ? 'virtualDevice.validation.range.angValue' : 'virtualDevice.validation.format.dataValue';
      return {
        isValid: false,
        error: rowNumber ? t(errorKey, { row: rowNumber }) : `${fieldName} value out of range`
      };
    }
    return { isValid: true };
  }

  /**
   * 获取字段映射配置（用于Excel导入导出）
   * @returns 字段映射对象
   */
  static getFieldMapping(): { [key: string]: string } {
    return {
      [t("virtualDevice.columns.index")]: "index",
      [t("virtualDevice.columns.name")]: "name",
      [t("virtualDevice.columns.description")]: "description",
      [t("virtualDevice.columns.dataValue")]: "dataValue",
      [t("virtualDevice.columns.ang")]: "ang",
    };
  }

  /**
   * 获取Excel列定义
   * @returns 列定义数组
   */
  static getColumnDefinitions() {
    return [
      { header: t("virtualDevice.columns.index"), key: "index", width: 10 },
      { header: t("virtualDevice.columns.name"), key: "name", width: 20 },
      { header: t("virtualDevice.columns.description"), key: "description", width: 30 },
      { header: t("virtualDevice.columns.dataValue"), key: "dataValue", width: 15 },
      { header: t("virtualDevice.columns.ang"), key: "ang", width: 15 }
    ];
  }
}

/**
 * 前端校验规则（与后端保持一致）
 * 可用于前端表单校验
 */
export const VirtualDeviceValidationRules = {
  name: {
    required: true,
    message: () => t("virtualDevice.validation.required.name", { row: "" }).replace("：", "")
  },
  dataValue: {
    type: 'number',
    message: () => t("virtualDevice.validation.format.dataValue", { row: "" }).replace("：", "")
  },
  ang: {
    type: 'number',
    min: -360,
    max: 360,
    message: () => t("virtualDevice.validation.range.angValue", { row: "" }).replace("：", "")
  },
  filePath: {
    required: true,
    pattern: /\.xlsx$/i,
    message: () => t("virtualDevice.validation.format.fileExtension")
  }
};