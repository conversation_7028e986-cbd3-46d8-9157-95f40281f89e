<!-- 组态分类详情 -->
<template>
  <div class="device-tabs" :style="deviceListStyle.deviceNotEmpty">
    <el-tabs
      v-if="initDeviceList.length > 0 && hasCustomPage == false && hasEditPage == false"
      v-model="activeTab"
      type="border-card"
      @tab-click="tabClick"
    >
      <el-tab-pane v-for="item in initDeviceList" :key="item.name" :label="item.ip" :name="item.id">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><ChromeFilled /></el-icon>
            <span>{{ activeViewTab }}({{ item.ip }})</span>
          </span>
        </template>
        <Configure :device-id="item.id" />
      </el-tab-pane>
    </el-tabs>
    <el-tabs v-else-if="hasCustomPage" v-model="activeCustomTab" type="border-card">
      <el-tab-pane :label="t('hmi.device.configures.customComponent')" :name="t('hmi.device.configures.customComponent')">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><ChromeFilled /></el-icon>
            <span>{{ t("hmi.device.configures.customComponent") }}</span>
          </span>
        </template>
        <CustomConfigure />
      </el-tab-pane>
    </el-tabs>
    <el-tabs v-else-if="hasEditPage" v-model="activeEditTab" type="border-card">
      <el-tab-pane :name="activeEditTab">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><ChromeFilled /></el-icon>
            <span>{{ activeEditTab }}</span>
          </span>
        </template>
        <EditConfigure />
      </el-tab-pane>
    </el-tabs>
    <div v-else class="device-item-empty flx-center" :style="deviceListStyle.deviceEmpty">
      <img src="@/assets/images/404.png" class="not-img" />
      <div class="not-detail">
        <!-- <h2>404</h2> -->
        <h4>{{ t("hmi.device.configures.selectDevice") }}</h4>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ComputedRef, ref } from "vue";
import { find } from "lodash";
import { TabsPaneContext } from "element-plus";
import { useDebugStore } from "@/stores/modules/debug";
import { filter, forEach, set } from "lodash";
import { useGlobalStore, useHmiStore } from "@/stores/modules";
import { DebugDeviceInfo } from "@/stores/interface";
import Configure from "@/views/biz/hmi/device/components/Configure.vue";
import mittBus from "@/utils/mittBus";
import CustomConfigure from "@/views/biz/hmi/device/components/CustomConfigure.vue";
import EditConfigure from "@/views/biz/hmi/device/components/EditConfigure.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const activeTab = ref();
const activeCustomTab = ref(t("hmi.device.configures.customComponent"));
const hasCustomPage = ref(false);
const hasEditPage = ref(false);
const currentDevice = ref<DebugDeviceInfo>({
  id: "",
  name: "",
  ip: "",
  port: "",
  encrypted: false,
  prjType: 1,
  deviceType: 1,
  isConnect: false,
  isActive: false,
  connectTimeout: 10000,
  connectTime: ""
});
const hmiStore = useHmiStore();
const activeViewTab = computed(() => {
  if (hmiStore.hmiInfo.currConfigure) {
    if (hmiStore.hmiInfo.currConfigure.type && hmiStore.hmiInfo.currConfigure.type != "hmi") {
      return hmiStore.hmiInfo.currConfigure.label;
    } else {
      return hmiStore.hmiInfo.currConfigure?.label;
    }
  }

  return currentDevice.value.ip;
});
const activeEditTab = ref("");
const debugStore = useDebugStore();
const globalStore = useGlobalStore();
const deviceListStyle = computed<any>(() => {
  let offset = 51;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return {
    deviceEmpty: { height: `calc(100vh  - ${offset}px - 2px)` },
    deviceNotEmpty: { height: `calc(100vh  - ${offset}px)` }
  };
});
const tabClick = (tabItem: TabsPaneContext) => {
  const deviceId = tabItem.props.name as string;
  forEach(debugStore.deviceList, item => {
    set(item, "isActive", false);
  });
  const device = find(debugStore.deviceList, { id: deviceId });
  if (device) {
    device.isActive = true;
    debugStore.setCurrDevice(device);
  }
};

const initDeviceList: ComputedRef<DebugDeviceInfo[]> = computed(() => {
  const deviceListAll = debugStore.deviceList;
  deviceListAll.forEach(item => {
    if (item.isActive) {
      activeTab.value = item.id;
      currentDevice.value = item;
    }
  });
  return [
    ...filter(deviceListAll, obj => {
      return obj.isActive;
    })
  ];
});

const toCustomConfigure = (param: boolean) => {
  hasCustomPage.value = param;
  if (param) {
    activeCustomTab.value = t("hmi.device.configures.customComponent");
  }
};

const toEditConfigure = (param: boolean) => {
  hasEditPage.value = param;
  activeEditTab.value = t("hmi.device.configures.edit") + hmiStore.hmiInfo.currConfigure?.path;
};

mittBus.on("toCustomConfigure", async req => toCustomConfigure(req as boolean));
mittBus.on("toEditConfigure", async req => toEditConfigure(req as boolean));
</script>

<style scoped lang="scss">
.device-tabs {
  width: 100%;
  height: auto;
  margin-bottom: 5px;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__content) {
    padding: 3px;
  }
}
.device-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}
.not-img {
  margin-right: 50px;
}
.device-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.device-tabs .custom-tabs-label span {
  margin-left: 4px;
  vertical-align: middle;
}
.device-item-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 100%;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
}
</style>
