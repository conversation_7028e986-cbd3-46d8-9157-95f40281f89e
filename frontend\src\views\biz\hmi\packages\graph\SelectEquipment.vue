<template>
  <el-row>
    <el-col :span="24">
      <el-table :data="prop.data" style="height: 300px" :stripe="true">
        <el-table-column type="index" :label="t('hmi.graph.selectEquipment.sequence')" width="60"></el-table-column>
        <el-table-column prop="name" :label="t('hmi.graph.selectEquipment.name')"></el-table-column>
        <el-table-column prop="type" :label="t('hmi.graph.selectEquipment.type')"></el-table-column>
        <el-table-column prop="type" :label="t('hmi.graph.selectEquipment.symbol')">
          <template #default="scope">
            <el-space>
              <el-image :src="item.img" :key="index" v-for="(item, index) in scope.row.components" class="s-img">
                <template #error>
                  <el-icon :size="20">
                    <Picture></Picture>
                  </el-icon>
                </template>
              </el-image>
            </el-space>
          </template>
        </el-table-column>
        <el-table-column :label="t('hmi.graph.selectEquipment.operation')">
          <template #default="scope">
            <el-space>
              <el-icon
                :size="20"
                class="equipment-icon"
                @click="onCopy(scope.row)"
                :title="t('hmi.graph.selectEquipment.reference')"
              >
                <copy-document></copy-document>
              </el-icon>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
import { Picture, CopyDocument } from "@element-plus/icons-vue";
import { EquipmentData } from "./Graph";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const prop = defineProps<{
  data: unknown[];
}>();
const emit = defineEmits<{
  (e: "end", data: EquipmentData): void;
}>();

const onCopy = (data: EquipmentData) => {
  emit("end", data);
};
</script>
<style>
.s-img {
  width: 32px;
  height: 32px;
}
</style>
