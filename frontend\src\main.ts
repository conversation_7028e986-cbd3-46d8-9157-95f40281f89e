import { createApp } from "vue";
import App from "./App.vue";

// 核心样式 - 立即加载
import "@/styles/reset.scss";
import "@/styles/common.scss";
import "virtual:uno.css";

// Element Plus 样式 - 立即加载
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import "@/styles/element-dark.scss";
import "@/styles/element.scss";

// SVG 图标注册
import "virtual:svg-icons-register";

// 核心依赖 - 立即加载
import ElementPlus from "element-plus";
import router from "@/routers";
import pinia from "@/stores";
import directives from "@/directives/index";
import errorHandler from "@/utils/errorHandler";
import I18n from "@/languages/index";
// 右键菜单（同步加载以确保可用）
import "default-passive-events";
import contextmenu from "v-contextmenu";
import "v-contextmenu/dist/themes/default.css";

// 异步加载的依赖
const loadAsyncDependencies = async () => {
  // 动态导入非关键依赖
  const [hljsVuePlugin, BLRow, BLCol, { downloadAndInstall }] = await Promise.all([
    import("@highlightjs/vue-plugin"),
    import("@/components/Common/BLRow.vue"),
    import("@/components/Common/BLCol.vue"),
    import("@/utils/iconify")
  ]);

  // 异步加载样式
  await Promise.all([
    import("@/assets/iconfont/iconfont.scss"),
    import("@/assets/iconfontPlus/iconfont.scss"),
    import("@/assets/fonts/font.scss"),
    import("highlight.js/styles/atom-one-dark.css")
  ]);

  return { hljsVuePlugin, BLRow, BLCol, downloadAndInstall };
};

// ECharts 按需加载
const loadECharts = async () => {
  const [
    { default: echarts },
    { LineChart, BarChart, PieChart },
    { TitleComponent, TooltipComponent, LegendComponent, GridComponent },
    { CanvasRenderer }
  ] = await Promise.all([import("echarts/core"), import("echarts/charts"), import("echarts/components"), import("echarts/renderers")]);

  echarts.use([LineChart, BarChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer]);

  return echarts;
};

// 创建应用实例
const app = createApp(App);

// 错误处理
app.config.errorHandler = errorHandler;

// 注册核心插件
app
  .use(ElementPlus, {
    size: "default",
    zIndex: 3000
  })
  .use(directives)
  .use(router)
  .use(pinia)
  .use(I18n)
  .use(contextmenu);

// 异步初始化应用
const initializeApp = async () => {
  try {
    // 并行加载异步依赖
    const [asyncDeps] = await Promise.all([loadAsyncDependencies(), loadECharts()]);

    const { hljsVuePlugin, BLRow, BLCol, downloadAndInstall } = asyncDeps;

    // 注册异步加载的插件
    app.use(hljsVuePlugin.default);

    // 注册全局组件
    app.component("BlRow", BLRow.default).component("BLCol", BLCol.default);

    // 按需注册 Element Plus 图标
    const iconList = [
      "Edit",
      "Delete",
      "Search",
      "Refresh",
      "Plus",
      "Minus",
      "ColdDrink",
      "Setting",
      "Notification",
      "CircleCheckFilled",
      "ChromeFilled",
      "CircleClose",
      "FolderDelete",
      "Remove",
      "DArrowLeft",
      "DArrowRight",
      "More"
    ];

    // 动态导入图标
    const { default: Icons } = await import("@element-plus/icons-vue");
    iconList.forEach(key => {
      app.component(key, Icons[key as keyof typeof Icons]);
    });

    // 预加载图标（非阻塞）
    downloadAndInstall();

    // 挂载应用
    app.mount("#app");

    // 延迟初始化 highlight.js（非关键）
    setTimeout(async () => {
      const { default: hljsCommon } = await import("highlight.js/lib/common");
      hljsCommon.highlightAuto("<h1>Highlight.js has been registered successfully!</h1>").value;
    }, 1000);
  } catch (error) {
    console.error("应用初始化失败:", error);
    // 降级处理：直接挂载基础应用
    app.mount("#app");
  }
};

// 使用 requestIdleCallback 优化初始化时机
if ("requestIdleCallback" in window) {
  (window as any).requestIdleCallback(initializeApp, { timeout: 2000 });
} else {
  setTimeout(initializeApp, 0);
}
