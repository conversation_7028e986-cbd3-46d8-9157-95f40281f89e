<template>
  <form-container
    v-model="visible"
    @update:model-value="val => emit('update:visible', val)"
    width="580px"
    @keyup.enter="onSubmit"
    @close="emit('cancel')"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon><Monitor /></el-icon>
        <span>{{ props.device ? t("device.deviceForm.title.edit") : t("device.deviceForm.title.add") }}</span>
      </div>
    </template>

    <el-form :model="form" label-width="180px">
      <el-form-item :label="t('device.deviceForm.name')" required>
        <el-input v-model="form.name" />
      </el-form-item>

      <el-form-item :label="t('device.deviceForm.ip')" required>
        <el-input v-model="form.ip" />
      </el-form-item>

      <el-form-item :label="t('device.deviceForm.port')" required>
        <el-input v-model="form.port" />
      </el-form-item>

      <el-form-item :label="t('device.deviceForm.encrypted')">
        <el-switch v-model="form.encrypted" />
      </el-form-item>

      <!-- 高级表单区域 -->
      <div v-show="isAdvancedVisible">
        <el-form-item :label="t('device.deviceForm.connectTimeout')">
          <el-input-number v-model="form.connectTimeout" />
        </el-form-item>

        <el-form-item :label="t('device.deviceForm.readTimeout')">
          <el-input-number v-model="form.readTimeout" />
        </el-form-item>

        <el-form-item :label="t('device.deviceForm.paramTimeout')">
          <el-input-number v-model="form.paramTimeout" />
        </el-form-item>

        <el-form-item :label="t('device.deviceForm.isVirtual')">
          <el-switch v-model="form.isVirtual" />
        </el-form-item>

        <!-- 更多高级字段... -->
      </div>

      <!-- 展开/收缩按钮 -->
      <el-form-item>
        <el-button
          link
          @click="toggleAdvanced"
          :icon="isAdvancedVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          class="advanced-toggle-btn"
        >
          {{ isAdvancedVisible ? t("device.deviceForm.advanced.hide") : t("device.deviceForm.advanced.show") }}
        </el-button>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="emit('cancel')">{{ t("device.deviceForm.buttons.cancel") }}</el-button>
      <el-button type="primary" @click="onSubmit">{{ t("device.deviceForm.buttons.confirm") }}</el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { DebugDeviceInfo } from "@/stores/interface";
import { Monitor } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
// import { useDebugStore } from "@/stores/modules/debug";
// const debugStore = useDebugStore();

const { t } = useI18n();

const props = defineProps<{
  device?: DebugDeviceInfo;
  visible: boolean;
}>();
const visible = props.visible;
const emit = defineEmits(["submit", "cancel", "update:visible"]);
const form = ref<DebugDeviceInfo>({
  id: "",
  name: "100",
  ip: "***************",
  port: "58000",
  encrypted: false,
  isVirtual: false, // 默认非虚拟化装置
  prjType: 1,
  deviceType: 1,
  isConnect: false,
  isActive: false,
  connectTimeout: 5000,
  readTimeout: 30000,
  paramTimeout: 30000,
  connectTime: ""
});
const isAdvancedVisible = ref(false);
const toggleAdvanced = () => {
  isAdvancedVisible.value = !isAdvancedVisible.value;
};

watch(
  () => props.device,
  newDevice => {
    if (newDevice) {
      form.value = {
        ...newDevice,
        // 保证向后兼容性，如果没有 isVirtual 属性则默认为 false
        isVirtual: newDevice.isVirtual ?? false
      };
    }
  },
  { immediate: true }
);

function validateIP(ip: string) {
  const pattern = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)$/;
  return pattern.test(ip);
}

function onSubmit() {
  if (!form.value.name.trim()) {
    ElMessage.error(t("device.deviceForm.messages.nameRequired"));
    return;
  }
  if (form.value.name.length > 10) {
    ElMessage.error(t("device.deviceForm.messages.nameTooLong"));
    return;
  }

  if (!validateIP(form.value.ip)) {
    ElMessage.error(t("device.deviceForm.messages.invalidIp"));
    return;
  }

  const port = parseInt(form.value.port);
  if (isNaN(port) || port < 1 || port > 65535) {
    ElMessage.error(t("device.deviceForm.messages.invalidPort"));
    return;
  }

  if (form.value.connectTimeout && form.value.connectTimeout < 100) {
    ElMessage.warning(t("device.deviceForm.messages.timeoutTooShort"));
    return;
  }

  if (form.value.readTimeout && form.value.readTimeout < 100) {
    ElMessage.warning(t("device.deviceForm.messages.timeoutTooShort"));
    return;
  }

  if (form.value.paramTimeout && form.value.paramTimeout < 100) {
    ElMessage.warning(t("device.deviceForm.messages.timeoutTooShort"));
    return;
  }
  const deviceData = {
    ...form.value,
    id: props.device?.id
  };
  emit("submit", deviceData);
  // emit("update:visible", false);
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}
.el-dialog {
  border-radius: 8px;
}
.el-form {
  padding: 16px;
  margin: 0 8px;
  border: 1px solid var(--el-color-primary-light-5);
  border-radius: 6px;
}
.el-form-item {
  margin-bottom: 18px;
}
.el-form-item__label {
  font-weight: 500;
  color: #606266;
}
.el-input,
.el-input-number {
  width: 100%;

  --el-input-border-color: var(--el-color-primary-light-5);
  --el-input-hover-border-color: var(--el-color-primary-light-5);
  --el-input-focus-border-color: var(--el-color-primary-light-5);
}
.el-input-number {
  margin-right: 8px;
}
.el-switch {
  margin-top: 4px;
}
.el-dialog__footer {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}
.el-button {
  min-width: 80px;
}
.el-button--primary:hover {
  background-color: var(--el-color-primary-light-5);
  border-color: var(--el-color-primary-light-5);
}
.advanced-toggle-btn {
  color: var(--el-color-primary);
}
</style>
