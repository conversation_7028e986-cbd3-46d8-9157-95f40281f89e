<template>
  <el-dialog v-model="visible" width="1200px">
    <template #title>
      <svg-icon icon="ep:collection" style="margin-right: 6px; color: var(--el-color-primary); vertical-align: middle" />
      {{ isEdit ? t("device.customMenu.editPointGroup") : t("device.customMenu.addPointGroup") }}
    </template>
    <div class="dialog-body">
      <!-- 顶部表单 -->
      <div class="top-form">
        <el-form :model="form" :rules="rules" ref="formRef" :inline="true" class="form-inline">
          <el-form-item :label="t('device.customMenu.menuName')" prop="name">
            <el-input v-model="form.name" :placeholder="t('device.customMenu.inputMenuName')" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="t('device.customMenu.menuDesc')" prop="desc">
            <el-input v-model="form.desc" :placeholder="t('device.customMenu.inputMenuDesc')" style="width: 300px" />
          </el-form-item>
          <el-form-item :label="t('device.customMenu.selectGroupType')" prop="groupType">
            <el-select
              v-model="form.groupType"
              clearable
              style="width: 200px"
              @change="onGroupTypeChange"
              :loading="isLoadingData"
            >
              <el-option value="ST" :label="t('device.customMenu.groupTypes.ST')" />
              <el-option value="MX" :label="t('device.customMenu.groupTypes.MX')" />
              <el-option value="SP" :label="t('device.customMenu.groupTypes.SP')" />
              <el-option value="SG" :label="t('device.customMenu.groupTypes.SG')" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- 选择器区域 -->
      <div class="selector-area">
        <div class="card mb10 pt0 pb0">
          <SelectFilter :data="selectFilterData" :default-values="selectFilterValues" @change="changeSelectFilter" />
        </div>
        <div
          class="selector-content"
          v-loading="false"
          :element-loading-text="loadingProgress || t('device.customMenu.loadingData')"
        >
          <div class="left">
            <div class="table-box">
              <ProTable
                ref="proTable"
                :columns="columns"
                :request-api="getItemList"
                highlight-current-row
                table-key="customGroupItems"
                :init-param="initParam"
                :request-auto="false"
                row-key="name"
                toolbar="false"
                :pagination="true"
                selection
                @selection-change="handleLeftSelectionChange"
              >
                <template #operation="scope">
                  <el-button link type="primary" @click="onAddItem(scope.row)">{{ t("common.add") }}</el-button>
                </template>
                <!-- 已移除表格header按钮 -->
              </ProTable>
            </div>
          </div>
          <div class="mid">
            <el-button type="primary" plain :disabled="leftSelectedItems.length === 0" @click="onAddSelectedItems">→</el-button>
            <el-button type="danger" plain :disabled="rightSelectedItems.length === 0" @click="onRemoveSelectedItems"
              >←</el-button
            >
          </div>
          <div class="right">
            <div class="title">{{ t("device.customMenu.selectedPoints") }} ({{ selectedItems.length }})</div>
            <ProTable
              ref="selectedProTable"
              :columns="selectedColumns"
              :data="selectedItems"
              highlight-current-row
              table-key="selectedGroupItems"
              row-key="name"
              toolbar="false"
              @row-click="onSelectedRowSelect"
              :pagination="true"
              selection
              @selection-change="handleRightSelectionChange"
            >
              <template #operation="scope">
                <el-button link type="danger" @click="onRemoveItem(scope.row)">{{ t("common.remove") }}</el-button>
              </template>
            </ProTable>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ t("device.customMenu.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">{{ t("device.customMenu.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
// 已移除Refresh图标导入
import { customInfoApi, type SelectedItem, type CustomGroup } from "@/api/modules/biz/debug/custominfo";
import { getUUID } from "@/utils";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import type { ColumnProps } from "@/components/ProTable/interface";
import SelectFilter from "@/components/SelectFilter/index.vue";
const { t } = useI18n();

const props = defineProps<{ deviceId: string }>();
const emit = defineEmits<{ (e: "confirm", group: CustomGroup, isEdit: boolean): void }>();
const visible = defineModel<boolean>("visible", { default: false });
const isEdit = defineModel<boolean>("isEdit", { default: false });

const modelValue = defineModel<CustomGroup | null>("model", { default: null });

const formRef = ref();
const form = reactive<{ uuid?: string; name: string; desc: string; groupType?: string }>({
  name: "",
  desc: "",
  groupType: undefined
});

// 异步验证菜单名称唯一性
const validateMenuName = (rule: any, value: string, callback: any) => {
  if (!value || value.trim() === "") {
    callback(new Error(t("device.customMenu.inputMenuName")));
    return;
  }

  // 编辑模式下，如果名称没有改变，跳过验证
  if (isEdit.value && modelValue.value && modelValue.value.name === value) {
    callback();
    return;
  }

  // 验证用户输入的名称（将作为 newname 保存）
  customInfoApi
    .validateMenuNameByDevice(props.deviceId, value.trim())
    .then(result => {
      if (result.code === 0) {
        if (result.data.isValid) {
          callback();
        } else {
          callback(new Error(result.data.message || t("device.customMenu.validation.nameFailed")));
        }
      } else {
        callback(new Error(t("device.customMenu.validation.nameRetry")));
      }
    })
    .catch(error => {
      console.error("名称验证出错:", error);
      callback(new Error(t("device.customMenu.validation.nameRetry")));
    });
};

const rules = {
  name: [
    { required: true, message: t("device.customMenu.inputMenuName"), trigger: "blur" },
    { validator: validateMenuName, trigger: "blur" }
  ],
  desc: [{ required: true, message: t("device.customMenu.inputMenuDesc"), trigger: ["blur", "change"] }],
  groupType: [{ required: true, message: t("device.customMenu.selectGroupType"), trigger: ["blur", "change"] }]
};

const selectedItems = ref<SelectedItem[]>([]);
const leftSelectedItems = ref<SelectedItem[]>([]);
const rightSelectedItems = ref<SelectedItem[]>([]);

function handleLeftSelectionChange(selection: SelectedItem[]) {
  leftSelectedItems.value = selection;
}

function handleRightSelectionChange(selection: SelectedItem[]) {
  rightSelectedItems.value = selection;
}
const isLoadingData = ref<boolean>(false);
const loadingProgress = ref<string>("");
const lastDeviceId = ref<string>("");
const proTable = ref<ProTableInstance>();
const selectedProTable = ref<ProTableInstance>();

interface SelectFilterOption {
  label: string;
  value: string;
}

interface SelectFilterItem {
  title: string;
  key: string;
  multiple: boolean;
  options: SelectFilterOption[];
}

// SelectFilter 数据
const selectFilterData = reactive<SelectFilterItem[]>([
  {
    title: t("device.customMenu.selectGroupType"), // 确保在语言文件中已添加对应翻译
    key: "menu",
    multiple: false,
    options: [] as SelectFilterOption[]
  }
]);

// 默认 selectFilter 参数
interface SelectFilterValues {
  menu?: string;
}
const selectFilterValues = ref<SelectFilterValues>({});

// 左侧表格配置项
const columns = reactive<ColumnProps[]>([
  {
    type: "selection",
    fixed: "left",
    width: 50
  },
  {
    type: "index",
    label: t("device.variable.sequenceNumber"),
    width: 70,
    fixed: "left"
  },
  {
    prop: "name",
    label: t("device.groupInfo.table.name"),
    search: {
      el: "input",
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    label: t("device.groupInfo.table.desc")
  },
  {
    prop: "operation",
    label: t("common.operation"),
    width: 100,
    fixed: "right"
  }
]);

// 右侧已选择项表格配置
const selectedColumns: ColumnProps[] = [
  {
    type: "selection",
    fixed: "left",
    width: 50
  },
  {
    type: "index",
    label: t("device.variable.sequenceNumber"),
    width: 70
  },
  {
    prop: "name",
    label: t("device.groupInfo.table.name"),
    width: 200
  },
  {
    prop: "desc",
    label: t("device.groupInfo.table.desc"),
    minWidth: 200
  },
  {
    prop: "operation",
    label: t("common.operation"),
    width: 100,
    fixed: "right"
  }
];

// 初始化请求参数
const initParam = reactive({
  groupType: "",
  pageSize: 100,
  pageNum: 1
});

// 切换选择过滤器
const changeSelectFilter = (value: { menu?: string }) => {
  proTable.value!.pageable.pageNum = 1;
  selectFilterValues.value.menu = value.menu;
  proTable.value?.search();
};

watch(visible, v => {
  if (v) {
    // 检查是否需要重新加载数据（设备切换或首次加载）
    const currentDeviceId = props.deviceId;
    if (lastDeviceId.value !== currentDeviceId) {
      console.log("设备切换或首次加载");
      lastDeviceId.value = currentDeviceId;
    }

    if (isEdit.value && modelValue.value) {
      form.uuid = modelValue.value.uuid;
      form.name = modelValue.value.name || "";
      form.desc = modelValue.value.desc || "";
      form.groupType = modelValue.value.keyword || undefined;
      selectedItems.value = Array.isArray(modelValue.value.items) ? [...modelValue.value.items] : [];

      if (form.groupType) {
        initParam.groupType = form.groupType;
        nextTick(async () => {
          // 先更新SelectFilter数据
          await onGroupTypeChange(form.groupType);
          // 然后刷新表格
          proTable.value?.search();
        });
      }
    } else {
      form.uuid = getUUID();
      form.name = "";
      form.desc = "";
      form.groupType = undefined;
      selectedItems.value = [];
      selectFilterValues.value = {};
      initParam.groupType = "";
      selectFilterData[0].options = [];
    }
  }
});

async function onGroupTypeChange(value?: string) {
  console.log(`onGroupTypeChange 被调用, value: ${value}`);

  if (!value) {
    selectFilterValues.value = {};
    initParam.groupType = "";
    return;
  }

  // 更新选择过滤器的值
  initParam.groupType = value;

  try {
    isLoadingData.value = true;
    loadingProgress.value = `${t("device.customMenu.loadingGroupTypeDataSingle")} ${value}...`;

    // 加载该组类型下的所有菜单数据
    const res = await customInfoApi.getMenusByFcByDevice(props.deviceId, value);
    if (res.code === 0) {
      const menus = Array.isArray(res.data) ? res.data : [];

      // 更新SelectFilter选项
      const menuOptions = menus.map(menu => ({
        label: menu.desc || menu.name,
        value: menu.name
      }));

      // 更新SelectFilter数据
      selectFilterData[0].options = menuOptions;

      // 重置菜单选择
      selectFilterValues.value.menu = "";

      // 刷新表格数据
      if (proTable.value) {
        proTable.value.pageable.pageNum = 1;
        proTable.value.search();
      }
    }
  } catch (error) {
    console.error(`加载组类型 ${value} 数据失败:`, error);
    ElMessage.error(t("device.customMenu.loadGroupTypeError"));
  } finally {
    isLoadingData.value = false;
    loadingProgress.value = "";
  }
}

// 已移除forceReloadData函数

// 获取项目列表
const getItemList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.groupType = form.groupType;

  try {
    if (!form.groupType) {
      return { list: [], total: 0 };
    }

    isLoadingData.value = true;
    loadingProgress.value = `${t("device.customMenu.loadingGroupTypeDataSingle")} ${form.groupType}...`;

    const res = await customInfoApi.getMenusByFcByDevice(props.deviceId, form.groupType);
    console.log("API响应数据:", res); // 添加调试日志

    if (res.code === 0) {
      const menus = Array.isArray(res.data) ? res.data : [];
      console.log("处理前的菜单数据:", menus); // 添加调试日志

      // 处理数据
      let allItems: SelectedItem[] = [];
      menus.forEach((menu: any) => {
        // 如果SelectFilter选择了特定菜单，则只加载该菜单下的项目
        if (selectFilterValues.value.menu && selectFilterValues.value.menu !== menu.name) {
          return;
        }

        const items = (menu.items || []).map((it: any) => ({
          name: it.name,
          desc: it.desc,
          grp: it.grp,
          inf: it.inf,
          fc: menu.fc || form.groupType,
          unit: it.unit,
          type: it.type,
          menuName: menu.name,
          menuDesc: menu.desc
        }));
        allItems = [...allItems, ...items];
      });

      console.log("处理后的所有项目:", allItems); // 添加调试日志

      // 过滤掉已选择的项目
      const filteredItems = allItems.filter(i => !selectedItems.value.some(s => s.name === i.name));
      console.log("过滤后的项目:", filteredItems); // 添加调试日志

      // 搜索过滤
      let resultItems = filteredItems;
      if (newParams.name) {
        const searchText = newParams.name.toLowerCase();
        resultItems = resultItems.filter(
          item => item.name?.toLowerCase()?.includes(searchText) || item.desc?.toLowerCase()?.includes(searchText)
        );
      }

      // 分页处理
      const pageSize = newParams.pageSize || 10;
      const pageNum = newParams.pageNum || 1;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const pagedItems = resultItems.slice(start, end);

      console.log("最终返回的数据:", {
        list: pagedItems,
        total: resultItems.length
      }); // 添加调试日志

      return {
        data: {
          list: pagedItems,
          total: resultItems.length,
          pageNum: newParams.pageNum || 1,
          pageSize: newParams.pageSize || 10
        }
      };
    } else {
      console.error(`组类型 ${form.groupType} API返回错误:`, res.msg);
      return { list: [], total: 0 };
    }
  } catch (error) {
    console.error(`加载组类型 ${form.groupType} 数据失败:`, error);
    return { list: [], total: 0 };
  } finally {
    isLoadingData.value = false;
    loadingProgress.value = "";
  }
};

const candidateToAdd = ref<SelectedItem | null>(null);
const candidateToRemove = ref<SelectedItem | null>(null);

function onSelectedRowSelect(row: SelectedItem) {
  if (row) {
    candidateToRemove.value = row;
  }
}

function onAddItem(row: SelectedItem | null) {
  console.log("onAddItem called with:", row);
  if (!row) return;

  const exists = selectedItems.value.some(i => i.name === row.name);
  console.log("Item exists in selectedItems:", exists);

  if (!exists) {
    console.log("Adding new item:", row);
    selectedItems.value.push({ ...row });
    console.log("selectedItems after add:", selectedItems.value);

    // 直接刷新表格，不显示加载进度条
    proTable.value?.refresh(); // 刷新左侧表格，移除已添加的项目
    selectedProTable.value?.refresh(); // 刷新右侧表格
  }
  candidateToAdd.value = null;
}

function onAddSelectedItems() {
  console.log("onAddSelectedItems called with leftSelectedItems:", leftSelectedItems.value);
  if (leftSelectedItems.value.length === 0) return;

  // 使用 proTable 的 selectedList 获取选中的数据
  const selectedData = proTable.value?.selectedList || [];
  console.log("Selected data from proTable.selectedList:", selectedData);

  if (selectedData.length === 0) {
    console.log("No items selected in proTable.selectedList, falling back to leftSelectedItems");
    // 如果 selectedList 为空，则回退到原来的方式
    const newItems = leftSelectedItems.value.filter(item => !selectedItems.value.some(i => i.name === item.name));
    // 确保 newItems 符合 SelectedItem 类型
    const validNewItems = newItems.map(
      item =>
        ({
          name: item.name || "",
          desc: item.desc || "",
          grp: item.grp,
          inf: item.inf,
          fc: item.fc,
          unit: item.unit,
          type: item.type
        }) as SelectedItem
    );
    selectedItems.value = [...selectedItems.value, ...validNewItems];
  } else {
    // 添加选中项到右侧
    const newItems = selectedData.filter(item => !selectedItems.value.some(i => i.name === item.name));
    console.log("New items to add:", newItems);
    // 确保类型安全
    const validNewItems = newItems.map(
      item =>
        ({
          name: item.name || "",
          desc: item.desc || "",
          grp: item.grp,
          inf: item.inf,
          fc: item.fc,
          unit: item.unit,
          type: item.type
        }) as SelectedItem
    );
    selectedItems.value = [...selectedItems.value, ...validNewItems];
  }

  console.log("selectedItems after add:", selectedItems.value);

  // 直接刷新表格，不显示加载进度条
  proTable.value?.refresh(); // 刷新左侧表格，移除已添加的项目
  selectedProTable.value?.refresh(); // 刷新右侧表格
}

function onRemoveSelectedItems() {
  if (rightSelectedItems.value.length === 0) return;

  // 使用类型断言确保数据符合 SelectedItem 接口
  const selectedData = (selectedProTable.value?.selectedList || []) as SelectedItem[];
  console.log("Selected data from selectedProTable.selectedList:", selectedData);

  if (selectedData.length === 0) {
    console.log("No items selected in selectedProTable.selectedList, falling back to rightSelectedItems");
    // 如果 selectedList 为空，则回退到原来的方式
    selectedItems.value = selectedItems.value.filter(item => !rightSelectedItems.value.some(i => i.name === item.name));
  } else {
    // 从右侧移除选中项
    selectedItems.value = selectedItems.value.filter(item => !selectedData.some(i => i.name === item.name));
  }

  // 直接刷新表格，不显示加载进度条
  proTable.value?.refresh(); // 刷新左侧表格，显示被移除的项目
  selectedProTable.value?.refresh(); // 刷新右侧表格
}

function onRemoveItem(row: SelectedItem) {
  if (!row) return;

  // 使用 selectedProTable 的 selectedList 获取选中的数据
  const selectedData = selectedProTable.value?.selectedList || [];
  console.log("Selected data from selectedProTable.selectedList:", selectedData);

  if (selectedData.length > 0) {
    // 如果 selectedList 有数据，则移除所有选中的项目
    const namesToRemove = selectedData.map(item => item.name);
    selectedItems.value = selectedItems.value.filter(i => !namesToRemove.includes(i.name));
  } else {
    // 如果没有选中的项目，则移除当前行
    selectedItems.value = selectedItems.value.filter(i => i.name !== row.name);
  }

  candidateToRemove.value = null;

  // 直接刷新表格，不显示加载进度条
  proTable.value?.refresh(); // 刷新左侧表格，显示被移除的项目
  selectedProTable.value?.refresh(); // 刷新右侧表格
}

function onClose() {
  visible.value = false;
}

function onConfirm() {
  console.log("onConfirm - 开始提交自定义组");
  console.log("onConfirm - 当前表单数据:", form);

  // 确保选择了至少一个点
  if (selectedItems.value.length === 0) {
    console.error("onConfirm - 未选择任何点");
    ElMessage.error(t("device.customMenu.validation.selectAtLeastOne"));
    return;
  }

  // 使用表单验证（包括异步名称验证）
  formRef.value?.validate((valid: boolean, fields: any) => {
    console.log("onConfirm - Element Plus 表单校验结果:", { valid, fields });

    if (!valid) {
      console.error("自定义组表单校验失败:", fields);
      ElMessage.error(t("device.customMenu.validation.completeForm"));
      return; // 校验失败，不关闭对话框
    }

    console.log("onConfirm - 所有校验通过，继续处理");

    try {
      const payload: CustomGroup = {
        uuid: form.uuid || getUUID(),
        name: form.name, // 保留原始名称
        newname: form.name, // 用户输入的名称作为显示名称
        desc: form.desc,
        fc: form.groupType, // 使用用户选择的组类型
        keyword: form.groupType || "", // 将选择的组类型作为keyword保存
        inherit: undefined as any,
        items: selectedItems.value.map(item => ({
          name: item.name,
          desc: item.desc,
          grp: item.grp,
          inf: item.inf,
          fc: item.fc,
          unit: item.unit,
          type: item.type
        }))
      } as any;

      console.log("AddCustomPointGroupDialog - 提交数据:", payload);
      console.log("AddCustomPointGroupDialog - 选中的点数量:", selectedItems.value.length);
      console.log("AddCustomPointGroupDialog - 选中的组类型:", form.groupType);
      console.log("AddCustomPointGroupDialog - 是否编辑模式:", isEdit.value);

      // 通过 emit 通知父组件提交（父组件调用 addMenu/editMenu）
      emit("confirm", payload, isEdit.value);

      // 不在这里关闭对话框，由父组件处理成功后关闭
    } catch (e) {
      // 数据处理失败时，不关闭对话框
      console.error("AddCustomPointGroupDialog - 数据处理失败:", e);
      ElMessage.error(t("device.customMenu.errorAction"));
    }
  });
}

// moved defineEmits to top to avoid redeclaration
</script>

<style scoped>
.dialog-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.top-form {
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 16px;
}

.form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.form-inline .el-form-item {
  margin-bottom: 0;
}

.selector-area {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}

.selector-header {
  margin-bottom: 12px;
}

.selector-content {
  display: flex;
  gap: 0;
  height: 500px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  overflow: hidden;
}

.selector-content .left {
  flex: 1.5;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.selector-content .mid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: center;
  width: 60px;
  flex-shrink: 0;
  padding: 0 10px;
  margin-top: 100px; /* 调整垂直位置 */
}

.selector-content .right {
  flex: 1.2;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.title {
  margin: 6px 0;
  font-weight: 600;
  font-size: 14px;
}

/* 已移除.header样式 */

.empty-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 320px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}
</style>
