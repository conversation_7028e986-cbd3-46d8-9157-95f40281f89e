<template>
  <div class="table-box">
    <!-- 设备表单弹窗 -->
    <DeviceFormDialog v-if="showForm" v-model:visible="showForm" @submit="handleSubmit" @cancel="showForm = false" />

    <ProTable
      ref="proTable"
      :columns="columns"
      :request-auto="false"
      :data="tableData"
      highlight-current-row
      :init-param="initParam"
      :pagination="false"
      @select-all="selectionChange"
      @select="handleSelect"
      :data-callback="dataCallback"
      row-key="id"
      table-key="matrixDevice"
      style="flex: 1; min-height: 0"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-switch
            v-model="isReboot"
            :active-text="t('matrix.deviceList.reboot')"
            :inactive-text="t('matrix.deviceList.noReboot')"
            style="height: 10px"
          />
          <el-button type="primary" :disabled="hasTask" plain :icon="FolderAdd" @click="addDevice">{{
            t("matrix.deviceList.addDevice")
          }}</el-button>
          <el-button
            type="primary"
            :disabled="scope.selectedList.length == 0 || hasTask"
            :icon="ChatRound"
            @click="runTask(scope.selectedList)"
          >
            {{ t("matrix.common.execute") }}
          </el-button>
          <el-button type="success" :disabled="hasTask" :icon="Upload" @click="importFile">{{
            t("matrix.common.import")
          }}</el-button>
          <el-button type="success" :disabled="hasTask" :icon="Download" @click="exportFile">{{
            t("matrix.common.export")
          }}</el-button>
          <el-button
            type="danger"
            :disabled="scope.selectedList.length == 0 || hasTask"
            :icon="Delete"
            plain
            @click="batchDelete(scope.selectedList)"
          >
            {{ t("matrix.common.delete") }}
          </el-button>
          <el-button type="danger" :disabled="hasTask" :icon="Delete" plain @click="batchClear">
            {{ t("matrix.common.clear") }}
          </el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :disabled="hasTask" :icon="Upload" @click="handleMoveUp(scope.row)">{{
          t("matrix.common.moveUp")
        }}</el-button>
        <el-button type="primary" link :disabled="hasTask" :icon="Download" @click="handleMoveDown(scope.row)">{{
          t("matrix.common.moveDown")
        }}</el-button>
        <el-button type="primary" link :disabled="hasTask" :icon="Delete" @click="deleteSingleFile(scope.row)">{{
          t("matrix.common.delete")
        }}</el-button>
      </template>
    </ProTable>
    <ProgressDialog ref="progressDialog"></ProgressDialog>
  </div>
</template>

<script setup lang="tsx" name="deviceList">
import { ref, reactive, computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { FolderAdd, Delete, Download, Upload, ChatRound } from "@element-plus/icons-vue";
import { matrixApi } from "@/api/modules/biz/matrix/matrix";
import { deviceOperateApi } from "@/api/modules/biz/debug/deviceinfomenu";
import DeviceFormDialog from "../../debug/device/dialog/DeviceFormDialog.vue";
import { createScopeDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import ProgressDialog from "../../debug/device/dialog/ProgressDialog.vue";
import { DeviceItem } from "@/stores/modules/deviceItem";
import { DebugDeviceInfo } from "@/stores/interface";
import { IECNotify, MatrixTaskItem } from "@/api";
import { getUUID } from "@/utils";
import { useMatrixStore } from "@/stores/modules/matrix";
import { storeToRefs } from "pinia";
import { UpadRpcFileDownloadItem } from "@/api/interface/biz/debug/fileitem";
import { ipc } from "@/api/request/ipcRenderer";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules";

const { t } = useI18n();
const globalStore = useGlobalStore();
const matrixStire = useMatrixStore();
const hasTask = ref(false);
const { devicelist, selectDeviceIds } = storeToRefs(matrixStire);
const tableData = devicelist;
const selectIds = selectDeviceIds;
const isReboot = ref(false);
const { addConsole } = createScopeDebugStore("matrix")();
const showForm = ref(false);
const progressDialog = ref();
const proTable = ref<ProTableInstance>();

// 动态计算表格容器高度
const tableContainerHeight = computed(() => {
  // 获取页面可用高度
  const windowHeight = window.innerHeight;
  // 头部区域高度（如有固定头部可写死或动态获取）
  const headerHeight = 64; // 例如顶部导航栏高度
  // 控制台区域高度（如有固定高度可写死或动态获取）
  const consoleHeight = globalStore.consoleHeight || 200; // 200为默认控制台高度
  // 其它可能的间距
  const margin = 24; // 额外边距
  // 计算表格区域高度
  return `${windowHeight - headerHeight - consoleHeight - margin}px`;
});

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

// const tableData = ref<DeviceItem[]>([]);

const deleteSingleFile = async (row: any) => {
  console.log("deleteSingleFile", row);

  // 调用后端API删除装置
  try {
    const res = await deviceOperateApi.removeDeviceCfg(row.id);
    if (res.code === 0) {
      addConsole(t("matrix.deviceList.deviceDeleted", { ip: row.ip }));
      // 从前端表格中移除装置
      nextTick(() => {
        proTable.value?.element?.toggleRowSelection(row, false);
      });
      tableData.value = tableData.value.filter(item => row.id !== item.id);
    } else {
      addConsole(t("matrix.deviceList.deviceDeleteFailed", { ip: row.ip, msg: res.msg }));
    }
  } catch (error) {
    addConsole(t("matrix.deviceList.deviceDeleteFailed", { ip: row.ip, msg: error }));
  }
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

const runTask = async (rows: any[]) => {
  console.log("runTask", rows);
  const { downlist, selectDownIds, paramList, selectParamIds, remoteParentPath } = matrixStire;
  const items: MatrixTaskItem[] = [];
  const downfiles = downlist.filter(item => selectDownIds.includes(item.path));
  const orderMap = new Map();
  downlist.map((item, index) => {
    orderMap.set(item.path, index);
  });
  const sortDownfiles = [...downfiles].sort((a, b) => {
    const aIndex = orderMap.get(a.path);
    const bIndex = orderMap.get(b.path);
    return aIndex - bIndex;
  });

  const params = paramList.filter(item => selectParamIds.includes(item.paramName));
  console.log(params);

  const orderParamMap = new Map();
  paramList.map((item, index) => {
    orderParamMap.set(item.paramName, index);
  });
  const sortParamfiles = [...params].sort((a, b) => {
    const aIndex = orderMap.get(a.paramName);
    const bIndex = orderMap.get(b.paramName);
    return aIndex - bIndex;
  });

  const updateItems = sortParamfiles.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.paramName,
    v: item.value
  }));

  const fileItems: UpadRpcFileDownloadItem[] = [];
  sortDownfiles.forEach(row => {
    fileItems.push({
      filePath: row.path
    });
  });
  tableData.value.forEach(item => {
    item.status = "";
  });

  const orderDeviceMap = new Map();
  tableData.value.map((item, index) => {
    orderDeviceMap.set(item.id, index);
  });
  const sortRowfiles = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.id);
    const bIndex = orderMap.get(b.id);
    return aIndex - bIndex;
  });

  sortRowfiles.forEach(item => {
    item.status = "";
    item.percent = 0;
    // 创建纯净的设备数据副本，避免序列化问题
    const cleanDevice = {
      id: item.id,
      ip: item.ip,
      name: item.name,
      port: item.port,
      encrypted: item.encrypted,
      prjType: item.prjType,
      deviceType: item.deviceType,
      isConnect: item.isConnect,
      isActive: item.isActive,
      connectTimeout: item.connectTimeout,
      paramTimeout: item.paramTimeout,
      readTimeout: item.readTimeout,
      connectTime: item.connectTime,
      status: item.status,
      percentType: item.percentType,
      percent: item.percent,
      downFile: item.downFile,
      importParam: item.importParam
    };
    items.push({
      device: cleanDevice,
      downlist: toRaw(fileItems),
      paramList: toRaw(updateItems),
      isReboot: isReboot.value,
      remoteParentPath: remoteParentPath || "/dwld"
    });
  });
  hasTask.value = true;
  const response = await matrixApi.runTask(items);
  // if (Number(response.code) === 0) {
  //   addConsole("所有任务执行完成");
  // } else {
  //   addConsole("任务执行失败: " + response.msg);
  // }
  console.log(response);
  hasTask.value = false;
};

// 上移操作
const handleMoveUp = (row: any) => {
  const index = findIndexById(row.id);
  if (index <= 0) return;
  // 交换当前元素和上一个元素
  const newData = [...tableData.value];
  [newData[index - 1], newData[index]] = [newData[index], newData[index - 1]];
  tableData.value = newData;
};
// 查找行索引
const findIndexById = (id: string) => {
  return tableData.value.findIndex(item => item.id === id);
};
// 下移操作
const handleMoveDown = (row: any) => {
  const index = findIndexById(row.id);
  if (index >= tableData.value.length - 1) return;
  // 交换当前元素和下一个元素
  const newData = [...tableData.value];
  [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
  tableData.value = newData;
};
const handleSubmit = (device: DebugDeviceInfo) => {
  console.log(device);
  // 检查ip和端口是否已存在
  const isExist = tableData.value.some(
    item => item.ip === device.ip && item.port === device.port && item.id !== device.id // 排除当前编辑的设备
  );
  if (isExist) {
    ElMessage.warning(t("matrix.deviceList.deviceExists"));
    return;
  }
  const debugInfo: DeviceItem = {
    ...device,
    id: String(getUUID()),
    status: "",
    percentType: "",
    percent: 0,
    downFile: true,
    importParam: true
  };
  tableData.value.push(debugInfo);
  showForm.value = false;
};
// 表格配置项
const columns = reactive<ColumnProps<DeviceItem>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("matrix.common.index"), fixed: "left", width: 70 },
  {
    prop: "name",
    label: t("matrix.deviceList.deviceName")
  },
  {
    prop: "ip",
    label: t("matrix.deviceList.deviceAddress")
  },
  {
    prop: "port",
    label: t("matrix.deviceList.devicePort")
  },
  {
    prop: "encrypted",
    label: t("matrix.deviceList.isEncrypted"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-switch
            v-model={scope.row.encrypted}
            active-text={t("matrix.deviceList.encrypted")}
            inactive-text={t("matrix.deviceList.notEncrypted")}
            style="height:10px;"
            onChange={(value: string) => handleChange(value, scope.row)}
          />
        </div>
      );
    }
  },
  {
    prop: "downFile",
    label: t("matrix.deviceList.downloadFile"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-switch
            v-model={scope.row.downFile}
            active-text={t("matrix.common.yes")}
            inactive-text={t("matrix.common.no")}
            style="height:10px;"
            onChange={(value: string) => handleChange(value, scope.row)}
          />
        </div>
      );
    }
  },
  {
    prop: "importParam",
    label: t("matrix.deviceList.importParam"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-switch
            v-model={scope.row.importParam}
            active-text={t("matrix.common.yes")}
            inactive-text={t("matrix.common.no")}
            style="height:10px;"
            onChange={(value: string) => handleChange(value, scope.row)}
          />
        </div>
      );
    }
  },
  {
    prop: "connectTimeout",
    label: t("matrix.deviceList.connectTimeout"),
    isShow: false
  },
  {
    prop: "paramTimeout",
    label: t("matrix.deviceList.paramTimeout"),
    isShow: false
  },
  {
    prop: "readTimeout",
    label: t("matrix.deviceList.readTimeout"),
    isShow: false
  },
  {
    prop: "percent",
    label: t("matrix.deviceList.progress"),
    render: scope => {
      return (
        <div>
          <el-progress
            percentage={scope.row.percent}
            text-inside={true}
            stroke-width={16}
            status={scope.row.percentType}
          ></el-progress>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: t("matrix.deviceList.status")
  },
  {
    prop: "operation",
    label: t("matrix.deviceList.operation"),
    fixed: "right",
    width: 280
  }
]);

const batchDelete = async (rows: any[]) => {
  console.log(rows);

  // 调用后端API删除装置
  for (const row of rows) {
    try {
      const res = await deviceOperateApi.removeDeviceCfg(row.id);
      if (res.code === 0) {
        addConsole(t("matrix.deviceList.deviceDeleted", { ip: row.ip }));
      } else {
        addConsole(t("matrix.deviceList.deviceDeleteFailed", { ip: row.ip, msg: res.msg }));
      }
    } catch (error) {
      addConsole(t("matrix.deviceList.deviceDeleteFailed", { ip: row.ip, msg: error }));
    }
  }

  // 从前端表格中移除装置
  const fileMap = rows.map(row => row.id);
  nextTick(() => {
    rows.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  });
  tableData.value = tableData.value.filter(item => !fileMap.includes(item.id));
};

// 导出文件列表
const exportFile = async () => {
  const defaultPath = t("matrix.deviceList.deviceListExcel");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("matrix.deviceList.exportDeviceList"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  // 导出路径不存在返回
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  console.log("selectPath:", selectPath);

  progressDialog.value.show();

  try {
    // 创建一个纯净的数据副本，避免序列化问题
    const deviceItems = toRaw(tableData.value).map((item, index) => ({
      index: index + 1,
      id: item.id,
      ip: item.ip,
      name: item.name,
      port: item.port,
      encrypted: item.encrypted,
      prjType: item.prjType,
      deviceType: item.deviceType,
      isConnect: item.isConnect,
      isActive: item.isActive,
      connectTimeout: item.connectTimeout,
      paramTimeout: item.paramTimeout,
      readTimeout: item.readTimeout,
      connectTime: item.connectTime,
      status: item.status,
      percentType: item.percentType,
      percent: item.percent,
      downFile: item.downFile,
      importParam: item.importParam
    }));
    const result = await matrixApi.exportDeviceList({
      path,
      deviceItems
    });
    // 判断返回结果中的code为0，提示成功，否则提示失败
    if (Number(result.code) === 0) {
      addConsole(t("matrix.deviceList.exportSuccess", { path }));
      ElMessageBox.alert(t("matrix.deviceList.exportSuccessMsg"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.deviceList.exportFail"));
      ElMessageBox.alert(t("matrix.deviceList.exportFailMsg"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.deviceList.exportFail"), error);
    ElMessageBox.alert(t("matrix.deviceList.exportFailMsg"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};

const importFile = async () => {
  console.log("importFile");
  const selectPath = await osControlApi.selectFileByParams({
    title: t("matrix.deviceList.importDeviceList"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });

  console.log("selectPath:", selectPath.path);

  // 导入路径不存在返回
  if (!selectPath.path) {
    return;
  }

  const path = String(selectPath.path);
  console.log("selectPath:", path);

  progressDialog.value.show();
  try {
    // 调用导入 API
    const response = await matrixApi.importDeviceList({ path });

    // 处理返回结果
    if (Number(response.code) === 0) {
      const resData = response.data;

      if (Array.isArray(resData)) {
        batchClear();
        resData.forEach(file => {
          const result = tableData.value.filter(item => item.id === file.path);
          if (result && result.length > 0) {
            ElMessage.error(t("matrix.deviceList.fileExists", { path: file.path }));
            addConsole(t("matrix.deviceList.fileExistsMsg", { path: file.path }));
          } else {
            const row: DeviceItem = {
              id: String(getUUID()),
              ip: file.ip,
              name: file.name,
              port: file.port,
              encrypted: file.encrypted,
              prjType: 0,
              deviceType: 0,
              isConnect: false,
              isActive: false,
              connectTime: "",
              status: "",
              percentType: "",
              percent: 0,
              connectTimeout: file.connectTimeout,
              paramTimeout: file.paramTimeout,
              readTimeout: file.readTimeout,
              downFile: file.downFile,
              importParam: file.importParam
            };
            tableData.value.push(row);
          }
        });

        tableData.value.forEach(row => {
          selectIds.value = [...new Set([...selectIds.value, row.id])];
        });

        nextTick(() => {
          tableData.value.forEach(row => {
            proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.id));
          });
        });
      }
      addConsole(t("matrix.deviceList.importSuccess"));
      ElMessageBox.alert(t("matrix.deviceList.importSuccessMsg"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.deviceList.importFail", { msg: response.msg }));
      ElMessageBox.alert(t("matrix.deviceList.importFailMsg", { msg: response.msg }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.deviceList.importFail"), error);
    ElMessageBox.alert(t("matrix.deviceList.importFailMsg"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};
const addDevice = () => {
  console.log("addDevice");
  showForm.value = true;
};
const batchClear = () => {
  console.log("batchClear");
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  selectIds.value = [];
  tableData.value = [];
};
const selectionChange = isSelectAll => {
  console.log("selectionChange", isSelectAll);
  const map = isSelectAll.map(item => item.id);
  tableData.value.forEach(row => {
    if (map.includes(row.id)) {
      selectIds.value = [...new Set([...selectIds.value, row.id])];
    } else {
      selectIds.value = selectIds.value.filter(id => id !== row.id);
    }
  });
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.id === row.id);
    if (isSelected) {
      selectIds.value = [...new Set([...selectIds.value, row.id])];
    } else {
      selectIds.value = selectIds.value.filter(id => id !== row.id);
    }
  });
};
// 状态消息映射函数，将后台返回的消息映射到前端国际化键
const translateStatusMessage = (message: string): string => {
  // 创建消息映射表，将后台可能返回的中文消息映射到国际化键
  const messageMap: Record<string, string> = {
    // 连接相关
    连接装置: "matrix.taskSteps.connect",
    装置连接失败: "matrix.messages.deviceConnectionFailed",

    // 下载相关
    执行文件下载: "matrix.messages.executeFileDownload",
    下载文件中: "matrix.messages.downloadingFile",
    下载文件失败: "matrix.messages.downloadFileFailed",
    文件下载执行完成: "matrix.messages.fileDownloadCompleted",

    // 导入相关
    执行定值导入: "matrix.messages.executeParamImport",
    定值格式校验失败: "matrix.messages.paramValidationFailed",
    定值导入失败: "matrix.messages.paramImportFailed",
    定值导入执行完成: "matrix.messages.paramImportCompleted",

    // 任务完成
    任务执行完成: "matrix.messages.taskCompleted",
    重启装置成功: "matrix.messages.deviceRebootSuccess"
  };

  // 检查是否有精确匹配
  if (messageMap[message]) {
    return t(messageMap[message]);
  }

  // 检查是否包含特定关键词的复合消息
  for (const [key, value] of Object.entries(messageMap)) {
    if (message.includes(key)) {
      // 对于包含文件路径或其他动态内容的消息，保留动态部分
      if (key === "下载文件中" && message.includes("：")) {
        const filePath = message.split("：")[1];
        return t(value) + "：" + filePath;
      }
      if (key === "定值导入失败" && message.includes("：")) {
        const errorMsg = message.split("：")[1];
        return t(value) + "：" + errorMsg;
      }
      return t(value);
    }
  }

  // 如果没有找到映射，返回原消息
  return message;
};

const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  const notifyData = notify.data as any;
  console.log(notifyData);
  const type = notifyData.type;
  const deviceId = notifyData.data.deviceId;
  const currentStep = notifyData.data.currentStep; // 状态
  const message = notifyData.data.message; // 状态
  const status = notifyData.data.status; // 状态
  const progress = notifyData.data.totalProgress; // 状态
  const row = proTable.value?.tableData.find(row => row.id === deviceId);
  let msg = "";
  if (type === "progress") {
    console.log(currentStep);
    msg = translateStatusMessage(message);
    row.percentType = "";
  }
  if (type === "result") {
    msg = translateStatusMessage(message);
    if (status == "error") {
      row.percentType = "exception";
    }
    if (status == "success") {
      row.percentType = "success";
    }
  }
  if (notify.type == "matrixDownload") {
    row.percent = progress;
    row.status = msg;
    return;
  }
};
onMounted(() => {
  loadData();
  ipc.on("matrix_notify", notifyMethod);
  // addAllListeners();
});

onBeforeUnmount(() => {
  ipc.removeAllListeners("matrix_notify");
});
const loadData = async () => {
  setTimeout(() => {
    nextTick(() => {
      tableData.value.forEach(row => {
        proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.id));
      });
    });
  }, 500);
};
</script>

<style lang="scss" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: v-bind(tableContainerHeight);
  overflow: hidden;
}
.header {
  margin-bottom: 8px;
}
</style>
