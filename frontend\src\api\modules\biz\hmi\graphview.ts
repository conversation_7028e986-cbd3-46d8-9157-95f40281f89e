import {
  GraphGetDataRequest,
  GraphRegisterRequest,
  GraphRemoteControlRequest,
  GraphRemoteSetRequest
} from "@/api/interface/biz/hmi";
import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/hmi/graphview/");

const graphViewApi = {
  // 注册
  register(data: GraphRegisterRequest) {
    return ipc.invoke<any>("register", data);
  },
  // 查询数据
  getData(requestData: GraphGetDataRequest) {
    return ipc.invoke<any>("getData", requestData);
  },
  remoteControl(requestData: GraphRemoteControlRequest) {
    return ipc.invoke<any>("remoteControl", requestData);
  },
  remoteSet(requestData: GraphRemoteSetRequest) {
    return ipc.invoke<any>("remoteSet", requestData);
  }
};

export { graphViewApi };
