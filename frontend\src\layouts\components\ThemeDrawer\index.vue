<template>
  <el-drawer v-model="drawerVisible" :title="t('layout.theme.title')" size="290px">
    <!-- 布局样式 -->
    <el-divider class="divider" content-position="center">
      <el-icon><Notification /></el-icon>
      {{ t("layout.theme.layout.title") }}
    </el-divider>
    <div class="layout-box">
      <el-tooltip effect="dark" :content="t('layout.theme.layout.columns')" placement="top" :show-after="200">
        <div :class="['layout-item layout-columns', { 'is-active': layout == 'columns' }]" @click="setLayout('columns')">
          <div class="layout-dark"></div>
          <div class="layout-light"></div>
          <div class="layout-content"></div>
          <el-icon v-if="layout == 'columns'">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip effect="dark" :content="t('layout.theme.layout.classic')" placement="top" :show-after="200">
        <div :class="['layout-item layout-classic', { 'is-active': layout == 'classic' }]" @click="setLayout('classic')">
          <div class="layout-dark"></div>
          <div class="layout-container">
            <div class="layout-light"></div>
            <div class="layout-content"></div>
          </div>
          <el-icon v-if="layout == 'classic'">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip effect="dark" :content="t('layout.theme.layout.transverse')" placement="top" :show-after="200">
        <div :class="['layout-item layout-transverse', { 'is-active': layout == 'transverse' }]" @click="setLayout('transverse')">
          <div class="layout-dark"></div>
          <div class="layout-content"></div>
          <el-icon v-if="layout == 'transverse'">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip effect="dark" :content="t('layout.theme.layout.vertical')" placement="top" :show-after="200">
        <div :class="['layout-item layout-vertical', { 'is-active': layout == 'vertical' }]" @click="setLayout('vertical')">
          <div class="layout-dark"></div>
          <div class="layout-container">
            <div class="layout-light"></div>
            <div class="layout-content"></div>
          </div>
          <el-icon v-if="layout == 'vertical'">
            <CircleCheckFilled />
          </el-icon>
        </div>
      </el-tooltip>
    </div>

    <!-- 全局主题 -->
    <el-divider class="divider" content-position="center">
      <el-icon><ColdDrink /></el-icon>
      {{ t("layout.theme.global.title") }}
    </el-divider>
    <div class="theme-item">
      <span>{{ t("layout.theme.global.primary") }}</span>
      <el-color-picker v-model="primary" :predefine="colorList" @change="changePrimary" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.global.dark") }}</span>
      <SwitchDark />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.global.grey") }}</span>
      <el-switch v-model="isGrey" @change="changeGreyOrWeak('grey', !!$event)" />
    </div>
    <div class="theme-item mb40">
      <span>{{ t("layout.theme.global.weak") }}</span>
      <el-switch v-model="isWeak" @change="changeGreyOrWeak('weak', !!$event)" />
    </div>

    <!-- 界面设置 -->
    <el-divider class="divider" content-position="center">
      <el-icon><Setting /></el-icon>
      {{ t("layout.theme.interface.title") }}
    </el-divider>

    <div class="theme-item">
      <span>{{ t("layout.theme.interface.watermark") }}</span>
      <el-switch v-model="watermark" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.breadcrumb") }}</span>
      <el-switch v-model="breadcrumb" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.breadcrumbIcon") }}</span>
      <el-switch v-model="breadcrumbIcon" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.tabs") }}</span>
      <el-switch v-model="tabs" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.tabsIcon") }}</span>
      <el-switch v-model="tabsIcon" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.footer") }}</span>
      <el-switch v-model="footer" />
    </div>
    <div class="theme-item">
      <span>{{ t("layout.theme.interface.drawerForm") }}</span>
      <el-switch v-model="drawerForm" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useTheme } from "@/hooks/useTheme";
import { useGlobalStore } from "@/stores/modules/global";
import { LayoutType } from "@/stores/interface";
import { DEFAULT_PRIMARY } from "@/config";
import mittBus from "@/utils/mittBus";
import SwitchDark from "@/components/SwitchDark/index.vue";
import { useI18n } from "vue-i18n";
import { Notification, CircleCheckFilled, ColdDrink, Setting } from "@element-plus/icons-vue";

const { changePrimary, changeGreyOrWeak, setAsideTheme } = useTheme();
const { t } = useI18n();

const globalStore = useGlobalStore();
const { layout, primary, isGrey, isWeak, watermark, breadcrumb, breadcrumbIcon, tabs, tabsIcon, footer, drawerForm } =
  storeToRefs(globalStore);

// 预定义主题颜色
const colorList = [DEFAULT_PRIMARY, "#ed1c29", "#00b04c", "#cfcfcf", "#656a6a", "#000000"];

// 设置布局方式
const setLayout = (val: LayoutType) => {
  globalStore.setGlobalState("layout", val);
  setAsideTheme();
};

// 打开主题设置
const drawerVisible = ref(false);
mittBus.on("openThemeDrawer", () => (drawerVisible.value = true));
</script>

<style scoped lang="scss">
@import "./index";
</style>
