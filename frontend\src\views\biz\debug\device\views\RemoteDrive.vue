<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      :data-callback="dataCallback"
      row-key="name"
      highlight-current-row
      @search="getData"
      :pagination="false"
      :tool-button="false"
      table-key="remoteDrive"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="Switch" @click="remoteDrive(scope.row)">{{
          t("device.remoteDrive.action")
        }}</el-button>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { deviceInfoMenutreeApi } from "@/api/modules/biz/debug";
import { ResultData } from "@/api/interface";
import { Switch } from "@element-plus/icons-vue";
import { CtlCheck, CtlMode, MenuIdName, SelectRequestData } from "@/api/interface/biz/debug/remote";
import { remoteControlApi } from "@/api/modules/biz/debug/remoteoperate";
import ProgressDialog from "../dialog/ProgressDialog.vue";

const { t } = useI18n();
const { debugIndex, addConsole } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const tableData = ref<DebugInfoItem[]>([]);
const progressDialog = ref();

const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const remoteDrive = async (row: any) => {
  console.log(row);
  showLoading();
  try {
    const param: SelectRequestData = {
      /** 控制源即name */
      ctlObj: row.name,
      /** 值 */
      ctlVal: "2",
      /** 模式 sbo=选控，direct=直控 */
      ctlModel: CtlMode.DIRECT,
      /** 当前时间iso8601格式 */
      opemTm: new Date().toISOString(),
      test: "0",
      check: CtlCheck.NO_CHECK
    };

    remoteConfirm(param, "execute", row);
  } finally {
    hideLoading();
  }
};

const remoteConfirm = async (param: SelectRequestData, command: string, row: any) => {
  if (command == "execute") {
    const res = await remoteControlApi.ykSelectValueConfirmByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.alert(t("device.remoteDrive.executeSuccess"), t("device.remoteDrive.prompt"), {
        confirmButtonText: t("device.remoteDrive.confirm"),
        type: "success"
      });
      addConsole(row.desc + t("device.remoteDrive.actionSuccess"));
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteDrive.error"), {
        confirmButtonText: t("device.remoteDrive.confirm"),
        type: "error"
      });
      addConsole(row.desc + t("device.remoteDrive.actionFailed") + t("device.remoteDrive.failureReason") + "：" + res.msg);
    }
  }
};

const dataCallback = (data: any) => {
  console.log("dataCallback:", data);
  return {
    list: data.list,
    total: data.total
  };
};

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};
const getData = async () => {
  // showLoading();
  try {
    console.log("getData");
    let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
    console.log(newParams);
    const param: MenuIdName = {
      id: props.deviceId,
      type: "submenu",
      names: [debugIndex.compData.get(props.deviceId).pname, debugIndex.compData.get(props.deviceId).name]
    };
    const res: ResultData<DebugInfoItem[]> = await deviceInfoMenutreeApi.getDeviceMenuItemByDevice(props.deviceId, param);
    if (!res.data) {
      throw new Error("Invalid response structure");
    }
    let resultData = res.data;
    if (newParams.name) {
      resultData = res.data?.filter(data => {
        return data.name.toLowerCase().includes(newParams.name.toLowerCase());
      });
      console.log(resultData);
    }
    if (newParams.desc) {
      resultData = res.data?.filter(data => {
        return data.desc.toLowerCase().includes(newParams.desc.toLowerCase());
      });
      console.log(resultData);
    }
    tableData.value = Array.isArray(resultData) ? resultData : [];
    proTable.value?.refresh();
  } finally {
    // hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  {
    type: "index",
    label: t("device.remoteDrive.sequence"),
    width: 60
  },
  {
    prop: "name",
    label: t("device.remoteDrive.shortAddress"),
    width: 300,
    search: {
      el: "input",
      tooltip: t("device.remoteDrive.enterToFilter"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    label: t("device.remoteDrive.description"),
    search: {
      el: "input",
      tooltip: t("device.remoteDrive.enterToFilterDesc"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  { prop: "operation", label: t("device.remoteDrive.operation"), fixed: "right", width: 250 }
]);
onMounted(() => {
  getData();
});
watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      console.log("grpName", newValue);
      proTable.value?.reset();
      getData();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
</style>
