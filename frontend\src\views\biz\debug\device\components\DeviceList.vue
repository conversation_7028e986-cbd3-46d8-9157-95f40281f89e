<template>
  <div class="scroll-container">
    <el-scrollbar v-if="searchForData.length > 0" class="scrollbar-list" :height="getTreeScrollbar()">
      <div
        v-for="item in searchForData"
        class="device-item flx-center card"
        :key="item.id"
        :id="item.ip"
        :class="fnClass(item)"
        v-contextmenu:contextmenu
        @click="handleListClick(item)"
        @contextmenu.prevent="handleContextMenuEvent($event, item)"
      >
        <!-- 样式 -->
        <div class="pinned-tag"></div>
        <!-- 连接状态 -->
        <el-badge :hidden="false">
          <svg-icon v-if="item.isConnect" icon="ep:circle-check-filled" class="status-icon connected"></svg-icon>
          <svg-icon v-else icon="ep:circle-close-filled" class="status-icon disconnected"></svg-icon>
        </el-badge>
        <!-- 装置 -->
        <div class="device-item-right">
          <div class="device-item-right-left">
            <div class="device-name flx-start">
              <span class="name-title">{{ item.ip + "(" + item.port + ")" }}</span>
            </div>
            <div class="device-item-right-bottom">
              {{ item?.name || t("device.list.unnamedDevice") }}
              <span class="device-item-right-right" :class="item.isConnect ? 'connected' : 'disconnected'">{{
                item.isConnect ? t("device.list.connected") : t("device.list.disconnected")
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备表单弹窗 -->
      <DeviceForm
        v-if="showForm"
        v-model:visible="showForm"
        :device="editForm"
        @submit="handleSubmit"
        @cancel="showForm = false"
      />

      <!-- 右键菜单 -->
      <contextmenu ref="contextmenu" :disabled="!isRight">
        <contextmenu-item v-for="item in contextMenuItems" :key="item.id" @click="handleClickMenuItem(item)">
          <svg-icon :icon="item.svgIcon" class="menu-svg" />
          <span> {{ item.text }}</span>
        </contextmenu-item>
      </contextmenu>
    </el-scrollbar>
    <div v-else class="device-item-empty flx-center" :style="deviceListStyle.deviceEmpty">
      <span>{{ t("device.list.noDeviceFound") }}</span>
    </div>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts">
import { ref, computed, ComputedRef, onMounted } from "vue";
import { filter, forEach, set } from "lodash";
import { Contextmenu, ContextmenuItem } from "v-contextmenu";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugDeviceInfo } from "@/stores/interface";
import DeviceForm from "../dialog/DeviceFormDialog.vue";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { useGlobalStore } from "@/stores/modules";
import { IECNotify, ResultData } from "@/api";
import { deviceConnectApi, deviceOperateApi } from "@/api/modules/biz/debug/deviceinfomenu";
import { ipc } from "@/api/request/ipcRenderer";
import mittBus from "@/utils/mittBus";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const globalStore = useGlobalStore();

const isRight = ref(true);
const contextMenuItemInfo = ref<DebugDeviceInfo>();
const debugStore = useDebugStore();
const progressDialog = ref();
const { t } = useI18n();

const contextMenuItems = computed(() => [
  {
    id: "connect",
    icon: "",
    svgIcon: "ant-design:link-outlined",
    text: t("device.list.connect")
  },
  {
    id: "edit",
    icon: "",
    svgIcon: "ant-design:edit-outlined",
    text: t("device.list.edit")
  },
  {
    id: "disconnect",
    icon: "",
    svgIcon: "ant-design:disconnect-outlined",
    text: t("device.list.disconnect")
  },
  {
    id: "messageMonitor",
    icon: "",
    svgIcon: "ant-design:monitor-outlined",
    text: t("device.list.messageMonitor")
  },
  {
    id: "remove",
    icon: "",
    svgIcon: "ant-design:delete-outlined",
    text: t("device.list.remove")
  }
]);

const deviceListStyle = computed<any>(() => {
  let offset = 0;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return { deviceEmpty: { height: `calc(100vh - 90px - ${offset}px)` } };
});

const searchForData: ComputedRef<DebugDeviceInfo[]> = computed(() => {
  const deviceList = debugStore.deviceList;
  const searchText = debugStore.searchDevice.toLowerCase();

  return [
    ...filter(deviceList, obj => {
      return (
        obj.ip.toLowerCase().includes(searchText) ||
        obj.port.toString().toLowerCase().includes(searchText) ||
        obj.name.toLowerCase().includes(searchText)
      );
    })
  ];
});

const getTreeScrollbar = () => {
  let offset = 0;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  return `calc(100vh - 90px - ${offset}px)`;
};

const fnClass = item => {
  if (item.isActive == true) {
    return "is-active";
  }
  return "";
};

// 右键菜单
const handleContextMenuEvent = (e, item) => {
  isRight.value = true;
  contextMenuItemInfo.value = item;
};

// 点击装置
const handleListClick = data => {
  console.log(t("device.list.handleClickLog"), data);
  setCurrDevice(data);
};

const setCurrDevice = data => {
  forEach(debugStore.deviceList, item => {
    set(item, "isActive", false);
  });
  data.isActive = true;
  debugStore.setCurrDevice(data);
};

const handleClickMenuItem = async item => {
  const data = contextMenuItemInfo.value;
  if (!data) return;
  setCurrDevice(data);
  debugStore.initReportData(data.id);
  if (item.id === "edit") {
    if (data?.isConnect) {
      ElMessage.warning(t("device.list.disconnectBeforeEdit"));
      return;
    }
    // 打开编辑弹窗
    editForm.value = data;
    showForm.value = true;
  } else if (item.id === "connect") {
    // 检查装置是否已连接
    if (data?.isConnect) {
      ElMessage.warning(t("device.list.deviceAlreadyConnected"));
      return;
    }
    // 调用装置连接接口
    progressDialog.value?.show();
    try {
      const res: ResultData = await deviceConnectApi.deviceConnectByRpc(data.id, data);
      if (res.code === 0) {
        data.isConnect = true;
        const nowTime =
          getCurrentData() +
          new Date().getHours() +
          ":" +
          (new Date().getMinutes() > 10 ? new Date().getMinutes() : "0" + new Date().getMinutes());
        data.connectTime = nowTime;
        // 通知组态设备连接成功
        mittBus.emit("deviceConnectSuccess", data);
        debugStore.addConsole(t("device.list.connectSuccess", { name: data.name }));
      } else if (res.code === 103) {
        debugStore.addConsole(t("device.list.connectExist", { name: data.name }));
      } else {
        debugStore.addConsole(t("device.list.connectFailed", { name: data.name }));
        if (res.data) {
          debugStore.addConsole(t("device.list.connectFailedReason") + res.data);
        }
      }
    } finally {
      progressDialog.value?.hide();
    }

    debugStore.initReportData(data.id);
  } else if (item.id === "disconnect") {
    const res: ResultData = await deviceConnectApi.disconnectDevice(data.id);
    if (res.code === 0) {
      data.isConnect = false;
      debugStore.addConsole(t("device.list.disconnectedSuccess", { name: data.name }));
    } else {
      debugStore.addConsole(t("device.list.operateFailed", { name: data.name }));
    }
  } else if (item.id === "messageMonitor") {
    if (!data?.isConnect) {
      ElMessage.warning(t("device.list.connectFirst"));
      return;
    }
    // 触发显示报文监视界面
    mittBus.emit("showMessageMonitor", data);
    debugStore.addConsole(t("device.list.messageMonitorOpened", { name: data.name }));
  } else if (item.id === "remove") {
    if (data?.isConnect) {
      ElMessage.warning(t("device.list.disconnectBeforeDelete"));
      return;
    }
    const index = debugStore.deviceList.findIndex(item => item.id === data.id);
    if (index >= 0) {
      const removceDevice = debugStore.deviceList[index];
      debugStore.removeDevice(removceDevice.id);
    }
    console.log(t("device.list.dataLog") + JSON.stringify(data), item);
  }
};

const showForm = ref(false);
const editForm = ref<DebugDeviceInfo>();

const handleSubmit = (device: DebugDeviceInfo) => {
  console.log(device);
  // 检查ip和端口是否已存在
  const isExist = debugStore.deviceList.some(
    item => item.ip === device.ip && item.port === device.port && item.id !== device.id // 排除当前编辑的设备
  );
  if (isExist) {
    ElMessage.warning(t("device.list.ipPortExist"));
    return;
  }
  if (device.id) {
    // 更新已有设备
    debugStore.updateDevice(device);
  } else {
    // 添加新设备
    debugStore.addDevice({
      ...device,
      id: "",
      prjType: 1,
      deviceType: 1,
      isConnect: false,
      isActive: false,
      connectTime: ""
    });
  }
  showForm.value = false;
};

const initData = async () => {
  const res: ResultData = await deviceOperateApi.getDeviceCfgList();
  if (res.data.length > 0) {
    debugStore.deviceList = res.data;
  }
  const allDevice: DebugDeviceInfo[] = debugStore.deviceList;
  allDevice.forEach((device: DebugDeviceInfo) => {
    if (device.isConnect) {
      device.isConnect = false;
    }
  });

  // 默认选中第一个装置
  if (allDevice.length > 0) {
    setCurrDevice(allDevice[0]);
  }
};

onMounted(async () => {
  await initData();
});

ipc.removeAllListeners("notify");
ipc.on("notify", (_event: unknown, notify: IECNotify) => {
  // 连接进度通知
  if (notify.type == "disconnect") {
    // 检查是否有deviceId，如果有则只更新对应设备的状态
    if (notify.deviceId) {
      const targetDevice = debugStore.deviceList.find(device => device.id === notify.deviceId);
      if (targetDevice) {
        targetDevice.isConnect = false;
        debugStore.addConsole(t("device.list.disconnectedNotify", { name: targetDevice.name }));

        // 如果断开的是当前选中的设备，也更新当前设备状态
        if (debugStore.currDevice.id === notify.deviceId) {
          debugStore.currDevice.isConnect = false;
        }
      }
    } else {
      // 兼容旧版本：如果没有deviceId，则更新当前设备状态
      debugStore.currDevice.isConnect = false;
      debugStore.addConsole(t("device.list.currentDisconnectedNotify"));
    }
  }
});

const getCurrentData = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

mittBus.on("afterImportConfig", async type => {
  if (type != "configure") {
    await initData();
  }
});
</script>

<style lang="scss" scoped>
.scroll-container {
  position: relative;
  width: 100%;
  border: 1px solid var(--el-border-color);
  .scrollbar-list {
    background: var(--el-bg-color);
  }
  .device-item {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 60px;
    padding: 12px 16px;
    margin: 3px;
    overflow: hidden;
    color: var(--el-text-color);
    cursor: pointer;
    user-select: none;
    &:hover {
      background: var(--bl-hover-bg-color);
    }

    // 添加分割线
    // &::after {
    //   content: "";
    //   position: absolute;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   height: 1px;
    //   background-color: var(--el-border-color);
    // }
    &:last-child::after {
      display: none; // 最后一个item不需要分割线
    }
    .pinned-tag {
      position: absolute;
      top: 3px;
      left: 3px;
      display: block;
      border: 6px solid var(--el-color-primary);
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-radius: 2px;
      opacity: 0.8;
    }
    .status-icon {
      margin-right: 10px;
      font-size: 35px;
      &.connected {
        color: limegreen;
      }
      &.disconnected {
        color: red;
      }
    }
    .device-item-right {
      position: relative;
      width: 100%;
      height: 44px;
      margin-top: 6px;
      .device-item-right-left {
        width: 100%;
        .device-name {
          max-width: 180px;
          max-height: 18px;
          font-size: 14px;
          line-height: 18px;
          color: var(--el-text-color);
          .name-title {
            padding-right: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .device-item-right-bottom {
        font-size: 12px;
      }
      .device-item-right-right {
        float: right;
        width: auto;
        overflow: hidden;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        text-overflow: ellipsis;
        white-space: nowrap;
        &.connected {
          color: limegreen;
        }
        &.disconnected {
          color: red;
        }
      }
    }
  }
  .device-item-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--el-text-color-secondary);
    background: var(--el-bg-color);
  }
  .is-active {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    background: var(--el-color-primary-light-9) !important;
  }
}
.contextmenu-item,
.contextmenu-item .menu-svg {
  display: flex;
  align-items: center;
}
.contextmenu-item:hover {
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-8);
}
.contextmenu-item:hover .menu-svg {
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-7);
}
</style>
