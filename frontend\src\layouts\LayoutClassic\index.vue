<!-- 经典布局 -->
<template>
  <el-container class="layout">
    <el-header>
      <div class="header-lf mask-image">
        <div class="logo flx-space-evenly">
          <img class="logo-img" :src="sysInfo.SYS_LOGO" alt="logo" />
          <!-- <span class="company-name-en">{{ $t("layout.header.company.englishName") }}</span>
          <div class="logo-mark">®</div>
          <span class="company-name-cn">&nbsp;{{ $t("layout.header.company.name") }}</span> -->
        </div>
        <ToolBarLeft />
      </div>
      <div class="header-center"></div>
      <ToolBarDrag />
      <div class="header-ri">
        <ToolBarRight />
      </div>
    </el-header>
    <el-container class="classic-content">
      <el-aside>
        <div class="aside-box" :style="{ width: isCollapse ? '65px' : '210px' }">
          <el-scrollbar>
            <el-menu
              :router="false"
              :default-active="activeMenu"
              :collapse="isCollapse"
              :unique-opened="accordion"
              :collapse-transition="false"
            >
              <SubMenu :menu-list="menuList" />
            </el-menu>
          </el-scrollbar>
          <div class="other flx-justify-between">
            <GlobalSetting id="globalSetting" v-model="place" />
            <ThemeQuickSwitch id="themeQuickSwitch" v-model="place" />
            <Language id="language" v-model="place" />
            <MoreInfo id="moreInfo" v-model="place" />
          </div>
        </div>
      </el-aside>
      <el-container class="classic-main">
        <Main />
      </el-container>
    </el-container>
  </el-container>
</template>

<script setup lang="ts" name="layoutClassic">
import { computed } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore, useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import Main from "@/layouts/components/Main/index.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import ToolBarLeft from "@/layouts/components/Header/ToolBarLeft.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import GlobalSetting from "@/layouts/components/Header/components/GlobalSetting.vue";
import ThemeQuickSwitch from "@/layouts/components/Header/components/ThemeQuickSwitch.vue";
import Language from "@/layouts/components/Header/components/Language.vue";
import MoreInfo from "@/layouts/components/Header/components/MoreInfo.vue";
import ToolBarDrag from "@/layouts/components/Header/ToolBarDrag.vue";

const route = useRoute();
const authStore = useAuthStore();
const configStore = useConfigStore();
const accordion = true;
const isCollapse = true;
const place: string = "right";
useI18n();
const menuList = computed(() => authStore.showMenuListGet);
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const sysInfo = computed(() => configStore.sysBaseInfoGet);
</script>

<style scoped lang="scss">
@import "./index";
</style>
