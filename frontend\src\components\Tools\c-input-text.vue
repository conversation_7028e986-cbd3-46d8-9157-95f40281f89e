<script lang="ts" setup>
import { generateRandomId } from "@/utils/random";
import { type UseValidationRule, useValidation } from "@/composable/validation";
import { useVModel } from "@vueuse/core";
const props = withDefaults(
  defineProps<{
    value?: string;
    id?: string;
    placeholder?: string;
    label?: string;
    readonly?: boolean;
    disabled?: boolean;
    validationRules?: UseValidationRule<string>[];
    validationWatch?: Ref<unknown>[];
    validation?: ReturnType<typeof useValidation>;
    isValid?: boolean;
    labelPosition?: "top" | "left";
    labelWidth?: string;
    labelAlign?: "left" | "right";
    clearable?: boolean;
    testId?: string;
    autocapitalize?: "none" | "sentences" | "words" | "characters" | "on" | "off" | string;
    autocomplete?: "on" | "off" | string;
    autocorrect?: "on" | "off" | string;
    spellcheck?: "true" | "false" | boolean;
    rawText?: boolean;
    type?: "text" | "password";
    multiline?: boolean;
    rows?: number | string;
    autosize?: boolean;
    autofocus?: boolean;
    monospace?: boolean;
  }>(),
  {
    value: "",
    id: generateRandomId,
    placeholder: "",
    label: undefined,
    readonly: false,
    disabled: false,
    validationRules: () => [],
    validationWatch: undefined,
    validation: undefined,
    isValid: true,
    labelPosition: "top",
    labelWidth: "auto",
    labelAlign: "left",
    clearable: false,
    testId: undefined,
    autocapitalize: undefined,
    autocomplete: undefined,
    autocorrect: undefined,
    spellcheck: undefined,
    rawText: false,
    type: "text",
    multiline: false,
    rows: 3,
    autosize: false,
    autofocus: false,
    monospace: false
  }
);
const emit = defineEmits(["update:value"]);
const value = useVModel(props, "value", emit);
const showPassword = ref(false);

const {
  id,
  placeholder,
  label,
  validationRules,
  labelPosition,
  readonly,
  disabled,
  multiline,
  rows,
  rawText,
  autofocus,
  monospace
} = toRefs(props);

const validation =
  props.validation ??
  useValidation({
    rules: validationRules,
    source: value,
    watch: props.validationWatch
  });

const textareaRef = ref<HTMLTextAreaElement>();
const inputRef = ref<HTMLInputElement>();
const inputWrapperRef = ref<HTMLElement>();

const htmlInputType = computed(() => {
  if (props.type === "password" && !showPassword.value) {
    return "password";
  }

  return "text";
});

function focus(): void {
  if (textareaRef.value) {
    textareaRef.value.focus();
  }

  if (inputRef.value) {
    inputRef.value.focus();
  }
}

function blur(): void {
  if (textareaRef.value) {
    textareaRef.value.blur?.();
  }

  if (inputRef.value) {
    inputRef.value.blur?.();
  }
}

onMounted(() => {
  if (autofocus.value) {
    focus();
  }
});

defineExpose({
  inputWrapperRef,
  focus,
  blur
});
</script>

<template>
  <div
    class="c-input-text"
    :class="{
      disabled,
      error: !validation.isValid || !isValid,
      'label-left': labelPosition === 'left',
      multiline
    }"
  >
    <label v-if="label" :for="id" class="label"> {{ label }} </label>

    <div class="feedback-wrapper">
      <div ref="inputWrapperRef" class="input-wrapper">
        <slot name="prefix" />

        <textarea
          v-if="multiline"
          :id="id"
          ref="textareaRef"
          v-model="value"
          class="input"
          :class="{
            'leading-5 !font-mono': monospace
          }"
          :placeholder="placeholder"
          :readonly="readonly"
          :disabled="disabled"
          :data-test-id="testId"
          :autocapitalize="autocapitalize ?? (rawText ? 'off' : undefined)"
          :autocomplete="autocomplete ?? (rawText ? 'off' : undefined)"
          :spellcheck="spellcheck ?? (rawText ? false : undefined)"
          :rows="rows"
        />

        <input
          v-else
          :id="id"
          ref="inputRef"
          v-model="value"
          :type="htmlInputType"
          class="input"
          :class="{
            'leading-5 !font-mono': monospace
          }"
          size="1"
          :placeholder="placeholder"
          :readonly="readonly"
          :disabled="disabled"
          :data-test-id="testId"
          :autocapitalize="autocapitalize ?? (rawText ? 'off' : undefined)"
          :autocomplete="autocomplete ?? (rawText ? 'off' : undefined)"
          :autocorrect="autocorrect ?? (rawText ? 'off' : undefined)"
          :spellcheck="spellcheck ?? (rawText ? false : undefined)"
        />

        <slot name="suffix" />
      </div>
      <span class="feedback"> {{ validation.message || "\u00a0" }} </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.c-input-text {
  display: inline-flex;
  flex-direction: column;
  width: 100%;
  &.label-left {
    flex-direction: row;
    align-items: baseline;
  }
  &.error {
    & > .input {
      border-color: var(--el-color-error);
      &:hover,
      &:focus {
        border-color: var(--el-color-error);
      }
      &:focus {
        background-color: var(--el-color-error);
      }
    }
    & .feedback {
      color: var(--el-color-error);
    }
  }
  & > .label {
    flex: 0 0 auto;
    padding-right: 12px;
    margin-bottom: 5px;
    text-align: left;
  }
  .feedback-wrapper {
    flex: 1 1 0;
    min-width: 0;
    & .success {
      color: var(--el-color-success);
    }
  }
  .input-wrapper {
    display: flex;
    flex-direction: row;
    padding: 0 4px 0 12px;
    overflow-y: auto;
    color: transparent;
    background-color: var(--bl-html-color);
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    transition: border-color 0.2s ease-in-out;
    .multiline {
      overflow: hidden;
      & > textarea {
        overflow-y: auto;
        font-family: inherit;
        font-size: inherit;
        color: var(--el-color-primary);
        word-break: break-word;
        overflow-wrap: break-word;
        white-space: pre-wrap;
        resize: none;
        border: none;
        outline: none;
        &::placeholder {
          color: var(--el-color-primary);
        }
      }
    }
    & > .input {
      flex: 1 1 0;
      min-width: 0;
      padding: 8px 0;
      overflow-y: auto;
      resize: none;
      scrollbar-width: thin;
      background-color: transparent;
      background-image: none;
      border: none;
      outline: none;
      box-shadow: none;
    }
    &:hover {
      border-color: var(--el-color-primary);
    }
    &:focus-within {
      background-color: var(--bl-html-color);
      border-color: var(--el-color-primary);
    }
  }
  &.error .input-wrapper {
    border-color: var(--el-color-error);
    &:hover,
    &:focus-within {
      border-color: var(--el-color-error);
    }
    &:focus-within {
      background-color: var(--bl-html-color);
    }
  }
  &.disabled .input-wrapper {
    opacity: 0.5;
    &:hover,
    &:focus-within {
      border-color: var(--el-border-color);
    }
    & > .input {
      cursor: not-allowed;
    }
  }
}
</style>
