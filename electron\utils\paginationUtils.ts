/**
 * 分页工具类
 * <AUTHOR>
 */

import { PageResponse } from "../interface/debug/pagination";

/**
 * 分页工具类
 */
export class PaginationUtils {
  /**
   * 创建分页响应对象
   * @param allData 全部数据
   * @param pageNum 当前页码
   * @param pageSize 每页数量
   * @returns 分页响应对象
   */
  static createPageResponse<T>(
    allData: T[], 
    pageNum: number = 1, 
    pageSize: number = 10
  ): PageResponse<T> {
    const total = allData.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, total);
    const pageData = allData.slice(startIndex, endIndex);

    return {
      list: pageData,
      total: total,
      pageNum: pageNum,
      pageSize: pageSize,
      totalPages: totalPages,
      hasNextPage: pageNum < totalPages,
      hasPrevPage: pageNum > 1
    };
  }

  /**
   * 验证分页参数
   * @param pageNum 页码
   * @param pageSize 每页数量
   * @returns 验证后的参数
   */
  static validatePageParams(pageNum?: number, pageSize?: number): { pageNum: number; pageSize: number } {
    const validPageNum = Math.max(1, pageNum || 1);
    const validPageSize = Math.min(Math.max(1, pageSize || 10), 100); // 限制最大100条
    
    return {
      pageNum: validPageNum,
      pageSize: validPageSize
    };
  }

  /**
   * 数组搜索过滤
   * @param data 原始数据
   * @param searchTerm 搜索词
   * @param searchFields 搜索字段
   * @returns 过滤后的数据
   */
  static filterData<T extends Record<string, any>>(
    data: T[], 
    searchTerm?: string, 
    searchFields: (keyof T)[] = []
  ): T[] {
    if (!searchTerm || !searchTerm.trim()) {
      return data;
    }

    const term = searchTerm.trim().toLowerCase();
    return data.filter(item => {
      if (searchFields.length === 0) {
        // 如果没有指定字段，搜索所有字符串字段
        return Object.values(item as Record<string, any>).some(value => 
          typeof value === 'string' && value.toLowerCase().includes(term)
        );
      } else {
        // 搜索指定字段
        return searchFields.some(field => {
          const value = item[field];
          return typeof value === 'string' && value.toLowerCase().includes(term);
        });
      }
    });
  }

  /**
   * 数组排序
   * @param data 原始数据
   * @param sortField 排序字段
   * @param sortOrder 排序顺序
   * @returns 排序后的数据
   */
  static sortData<T>(
    data: T[], 
    sortField?: keyof T, 
    sortOrder: 'asc' | 'desc' = 'asc'
  ): T[] {
    if (!sortField) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === bValue) return 0;

      let result = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        result = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        result = aValue - bValue;
      } else {
        result = String(aValue).localeCompare(String(bValue));
      }

      return sortOrder === 'desc' ? -result : result;
    });
  }

  /**
   * 自定义文件排序（按文件名数字前缀排序）
   * @param files 文件列表
   * @param order 排序顺序
   * @returns 排序后的文件列表
   */
  static sortFilesByNumericPrefix(
    files: any[], 
    order: 'asc' | 'desc' = 'desc'
  ): any[] {
    return [...files].sort((a, b) => {
      const getNumericPrefix = (fileName: string) => {
        const index = fileName.indexOf("_");
        if (index > 0) {
          const prefix = fileName.substring(0, index);
          const num = parseInt(prefix);
          return isNaN(num) ? 0 : num;
        }
        return 0;
      };
      
      const numA = getNumericPrefix(a.fileName);
      const numB = getNumericPrefix(b.fileName);
      
      return order === 'desc' ? numB - numA : numA - numB;
    });
  }
}