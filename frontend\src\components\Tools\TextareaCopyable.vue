<script setup lang="ts">
import { useCopy } from "@/composable/copy";
import { getTextMaxHeight } from "@/utils";
import { useI18n } from "vue-i18n";

const props = withDefaults(
  defineProps<{
    value: string;
    followHeightOf?: HTMLElement | null;
    language?: string;
    copyPlacement?: "top-right" | "bottom-right" | "outside" | "none";
    copyMessage?: string;
  }>(),
  {
    followHeightOf: null,
    language: "txt",
    copyPlacement: "top-right",
    copyMessage: "Copy to clipboard"
  }
);

const { value, language } = toRefs(props);
const { t } = useI18n();

const { copy } = useCopy({ source: value, createToast: false });
</script>

<template>
  <div class="text-area" style="overflow-x: hidden; width: 100%">
    <c-card relative>
      <el-scrollbar trigger="none">
        <el-config-provider>
          <el-input
            v-model="value"
            type="textarea"
            :language="language"
            spellcheck="false"
            :trim="false"
            data-test-id="area-content"
            :style="getTextMaxHeight('--heightLine', 290, false)"
          />
          <el-button
            class="input-with-button-floating-action-button"
            :style="getTextMaxHeight('--buttonLine', 245, false)"
            @click="copy()"
          >
            {{ t("common.copy") }}
          </el-button>
        </el-config-provider>
      </el-scrollbar>
    </c-card>
  </div>
</template>

<style lang="scss" scoped>
.text-area {
  .el-textarea {
    height: auto;
    --el-input-hover-border-color: var(--el-color-primary);
    :deep(.el-textarea__inner) {
      height: var(--heightLine);
      background-color: var(--bl-html-color);
      overflow-y: auto;
      scrollbar-width: thin;
      resize: none;
    }
  }
  .el-input-with-button {
    position: relative;
  }

  .input-with-button-textarea {
    width: 99%;
    box-sizing: border-box;
  }

  .input-with-button-floating-action-button {
    position: absolute;
    right: 15px;
    bottom: var(--buttonLine);
  }
}
</style>
