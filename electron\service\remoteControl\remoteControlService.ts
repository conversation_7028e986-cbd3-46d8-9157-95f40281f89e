"use strict";

import { logger } from "ee-core/log";
import * as net from "net";
import { v4 as uuidv4 } from "uuid";
import * as WebSocket from "ws";
import { EventEmitter } from "events";

/**
 * VNC连接配置接口
 */
interface VNCConnectionConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
  timeout?: number;
  quality?: "high" | "medium" | "low";
  autoReconnect?: boolean;
  shared?: boolean;
}

/**
 * VNC会话接口
 */
interface VNCSession {
  sessionId: string;
  host: string;
  port: number;
  username?: string;
  status: "connecting" | "connected" | "disconnected" | "error";
  createdAt: Date;
  lastActivity: Date;
  websocketUrl?: string;
  websocketPort?: number;
  connection?: net.Socket;
  websocketServer?: WebSocket.Server;
  config?: VNCConnectionConfig;
  reconnectAttempts?: number;
  maxReconnectAttempts?: number;
}

/**
 * VNC连接事件
 */
interface VNCConnectionEvents {
  connect: (session: VNCSession) => void;
  disconnect: (session: VNCSession) => void;
  error: (session: VNCSession, error: Error) => void;
  data: (session: VNCSession, data: Buffer) => void;
}

/**
 * 远程控制服务
 * 处理VNC连接和控制逻辑
 * <AUTHOR>
 * @class
 */
class RemoteControlService extends EventEmitter {
  private sessions: Map<string, VNCSession> = new Map();
  private websocketPortRange = { start: 6080, end: 6180 };
  private usedPorts: Set<number> = new Set();

  constructor() {
    super();
    this.setupCleanupTimer();
  }

  /**
   * 设置定期清理定时器
   */
  private setupCleanupTimer() {
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 60000); // 每分钟检查一次
  }

  /**
   * 清理不活跃的会话
   */
  private cleanupInactiveSessions() {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30分钟

    for (const [sessionId, session] of this.sessions) {
      if (now.getTime() - session.lastActivity.getTime() > inactiveThreshold) {
        logger.info(`[RemoteControlService] 清理不活跃会话: ${sessionId}`);
        this.disconnect(sessionId);
      }
    }
  }

  /**
   * 获取可用的WebSocket端口
   */
  private getAvailableWebSocketPort(): number {
    for (let port = this.websocketPortRange.start; port <= this.websocketPortRange.end; port++) {
      if (!this.usedPorts.has(port)) {
        this.usedPorts.add(port);
        return port;
      }
    }
    throw new Error("没有可用的WebSocket端口");
  }

  /**
   * 释放WebSocket端口
   */
  private releaseWebSocketPort(port: number) {
    this.usedPorts.delete(port);
  }

  /**
   * 测试VNC连接
   * @param connectionConfig 连接配置
   */
  async testConnection(connectionConfig: {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }): Promise<boolean> {
    return new Promise((resolve) => {
      logger.info(`[RemoteControlService] testConnection - 测试连接到 ${connectionConfig.host}:${connectionConfig.port}`);
      
      const socket = new net.Socket();
      const timeout = 5000; // 5秒超时

      const timer = setTimeout(() => {
        socket.destroy();
        logger.warn(`[RemoteControlService] testConnection - 连接超时: ${connectionConfig.host}:${connectionConfig.port}`);
        resolve(false);
      }, timeout);

      socket.connect(connectionConfig.port, connectionConfig.host, () => {
        clearTimeout(timer);
        socket.destroy();
        logger.info(`[RemoteControlService] testConnection - 连接成功: ${connectionConfig.host}:${connectionConfig.port}`);
        resolve(true);
      });

      socket.on("error", (error) => {
        clearTimeout(timer);
        logger.error(`[RemoteControlService] testConnection - 连接失败: ${connectionConfig.host}:${connectionConfig.port}`, error);
        resolve(false);
      });
    });
  }

  /**
   * 建立VNC连接
   * @param connectionConfig 连接配置
   */
  async connect(connectionConfig: VNCConnectionConfig): Promise<VNCSession> {
    logger.info(`[RemoteControlService] connect - 建立VNC连接到 ${connectionConfig.host}:${connectionConfig.port}`);

    const sessionId = uuidv4();
    const websocketPort = this.getAvailableWebSocketPort();
    const now = new Date();

    const session: VNCSession = {
      sessionId,
      host: connectionConfig.host,
      port: connectionConfig.port,
      username: connectionConfig.username,
      status: "connecting",
      createdAt: now,
      lastActivity: now,
      websocketPort,
      websocketUrl: `ws://localhost:${websocketPort}`,
      config: connectionConfig,
      reconnectAttempts: 0,
      maxReconnectAttempts: connectionConfig.autoReconnect ? 3 : 0
    };

    try {
      // 测试连接
      const isConnectable = await this.testConnection(connectionConfig);
      if (!isConnectable) {
        session.status = "error";
        this.releaseWebSocketPort(websocketPort);
        throw new Error(`无法连接到 ${connectionConfig.host}:${connectionConfig.port}`);
      }

      // 建立实际的VNC连接
      await this.establishVNCConnection(session);

      // 创建WebSocket服务器用于前端连接
      await this.createWebSocketServer(session);

      session.status = "connected";
      session.lastActivity = new Date();
      this.sessions.set(sessionId, session);

      logger.info(`[RemoteControlService] connect - VNC连接建立成功, 会话ID: ${sessionId}`);
      this.emit('connect', session);
      return session;
    } catch (error) {
      session.status = "error";
      this.sessions.set(sessionId, session);
      this.releaseWebSocketPort(websocketPort);
      logger.error(`[RemoteControlService] connect - VNC连接失败`, error);
      this.emit('error', session, error as Error);
      throw error;
    }
  }

  /**
   * 建立实际的VNC连接
   */
  private async establishVNCConnection(session: VNCSession): Promise<void> {
    return new Promise((resolve, reject) => {
      const socket = new net.Socket();
      const timeout = (session.config?.timeout || 15) * 1000;

      const timer = setTimeout(() => {
        socket.destroy();
        reject(new Error(`连接超时: ${session.host}:${session.port}`));
      }, timeout);

      socket.connect(session.port, session.host, () => {
        clearTimeout(timer);
        session.connection = socket;
        logger.info(`[RemoteControlService] VNC连接建立成功: ${session.host}:${session.port}`);
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timer);
        reject(error);
      });

      socket.on('data', (data) => {
        session.lastActivity = new Date();
        this.emit('data', session, data);
        // 这里应该处理VNC协议数据
      });

      socket.on('close', () => {
        logger.info(`[RemoteControlService] VNC连接关闭: ${session.sessionId}`);
        if (session.config?.autoReconnect && session.reconnectAttempts! < session.maxReconnectAttempts!) {
          this.attemptReconnect(session);
        }
      });
    });
  }

  /**
   * 创建WebSocket服务器
   */
  private async createWebSocketServer(session: VNCSession): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wss = new WebSocket.Server({ port: session.websocketPort! });

        wss.on('connection', (ws) => {
          logger.info(`[RemoteControlService] WebSocket客户端连接: ${session.sessionId}`);

          ws.on('message', (message) => {
            // 处理来自前端的消息，转发到VNC服务器
            if (session.connection) {
              session.connection.write(message);
            }
          });

          ws.on('close', () => {
            logger.info(`[RemoteControlService] WebSocket客户端断开: ${session.sessionId}`);
          });

          // 将VNC数据转发到WebSocket客户端
          if (session.connection) {
            session.connection.on('data', (data) => {
              if (ws.readyState === WebSocket.OPEN) {
                ws.send(data);
              }
            });
          }
        });

        session.websocketServer = wss;
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 尝试重连
   */
  private async attemptReconnect(session: VNCSession) {
    session.reconnectAttempts!++;
    logger.info(`[RemoteControlService] 尝试重连 ${session.reconnectAttempts}/${session.maxReconnectAttempts}: ${session.sessionId}`);

    setTimeout(async () => {
      try {
        await this.establishVNCConnection(session);
        session.status = "connected";
        session.reconnectAttempts = 0;
        logger.info(`[RemoteControlService] 重连成功: ${session.sessionId}`);
      } catch (error) {
        logger.error(`[RemoteControlService] 重连失败: ${session.sessionId}`, error);
        if (session.reconnectAttempts! >= session.maxReconnectAttempts!) {
          session.status = "error";
          this.emit('error', session, new Error('重连失败'));
        } else {
          this.attemptReconnect(session);
        }
      }
    }, 5000); // 5秒后重试
  }

  /**
   * 断开VNC连接
   * @param sessionId 会话ID
   */
  async disconnect(sessionId: string): Promise<boolean> {
    logger.info(`[RemoteControlService] disconnect - 断开VNC连接, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.warn(`[RemoteControlService] disconnect - 会话不存在: ${sessionId}`);
      return false;
    }

    try {
      // 关闭VNC连接
      if (session.connection) {
        session.connection.destroy();
        session.connection = undefined;
      }

      // 关闭WebSocket服务器
      if (session.websocketServer) {
        session.websocketServer.close();
        session.websocketServer = undefined;
      }

      // 释放WebSocket端口
      if (session.websocketPort) {
        this.releaseWebSocketPort(session.websocketPort);
      }

      session.status = "disconnected";
      this.sessions.delete(sessionId);

      logger.info(`[RemoteControlService] disconnect - VNC连接断开成功, 会话ID: ${sessionId}`);
      this.emit('disconnect', session);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] disconnect - 断开连接失败`, error);
      return false;
    }
  }

  /**
   * 获取连接状态
   * @param sessionId 会话ID（可选，不传则返回所有会话状态）
   */
  async getStatus(sessionId?: string): Promise<VNCSession | VNCSession[]> {
    if (sessionId) {
      logger.info(`[RemoteControlService] getStatus - 获取会话状态, 会话ID: ${sessionId}`);
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`会话不存在: ${sessionId}`);
      }
      return session;
    } else {
      logger.info(`[RemoteControlService] getStatus - 获取所有会话状态`);
      return Array.from(this.sessions.values());
    }
  }

  /**
   * 发送键盘事件
   * @param sessionId 会话ID
   * @param keyCode 键码
   * @param pressed 是否按下
   */
  async sendKeyEvent(sessionId: string, keyCode: number, pressed: boolean): Promise<boolean> {
    logger.info(`[RemoteControlService] sendKeyEvent - 发送键盘事件, 会话ID: ${sessionId}, 键码: ${keyCode}, 按下: ${pressed}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected" || !session.connection) {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 更新最后活动时间
      session.lastActivity = new Date();

      // 构造VNC键盘事件消息
      // VNC协议: KeyEvent消息格式
      const message = Buffer.alloc(8);
      message.writeUInt8(4, 0); // 消息类型: KeyEvent
      message.writeUInt8(pressed ? 1 : 0, 1); // down-flag
      message.writeUInt16BE(0, 2); // padding
      message.writeUInt32BE(keyCode, 4); // key

      session.connection.write(message);

      logger.info(`[RemoteControlService] sendKeyEvent - 键盘事件发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendKeyEvent - 发送键盘事件失败`, error);
      return false;
    }
  }

  /**
   * 发送鼠标事件
   * @param sessionId 会话ID
   * @param x X坐标
   * @param y Y坐标
   * @param buttonMask 按钮掩码
   */
  async sendMouseEvent(sessionId: string, x: number, y: number, buttonMask: number): Promise<boolean> {
    logger.info(`[RemoteControlService] sendMouseEvent - 发送鼠标事件, 会话ID: ${sessionId}, 坐标: (${x}, ${y}), 按钮: ${buttonMask}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected" || !session.connection) {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 更新最后活动时间
      session.lastActivity = new Date();

      // 构造VNC鼠标事件消息
      // VNC协议: PointerEvent消息格式
      const message = Buffer.alloc(6);
      message.writeUInt8(5, 0); // 消息类型: PointerEvent
      message.writeUInt8(buttonMask, 1); // button-mask
      message.writeUInt16BE(x, 2); // x-position
      message.writeUInt16BE(y, 4); // y-position

      session.connection.write(message);

      logger.info(`[RemoteControlService] sendMouseEvent - 鼠标事件发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendMouseEvent - 发送鼠标事件失败`, error);
      return false;
    }
  }

  /**
   * 发送Ctrl+Alt+Del
   * @param sessionId 会话ID
   */
  async sendCtrlAltDel(sessionId: string): Promise<boolean> {
    logger.info(`[RemoteControlService] sendCtrlAltDel - 发送Ctrl+Alt+Del, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected") {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 发送Ctrl+Alt+Del组合键
      await this.sendKeyEvent(sessionId, 17, true);  // Ctrl down
      await this.sendKeyEvent(sessionId, 18, true);  // Alt down
      await this.sendKeyEvent(sessionId, 46, true);  // Del down
      
      await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟
      
      await this.sendKeyEvent(sessionId, 46, false); // Del up
      await this.sendKeyEvent(sessionId, 18, false); // Alt up
      await this.sendKeyEvent(sessionId, 17, false); // Ctrl up

      logger.info(`[RemoteControlService] sendCtrlAltDel - Ctrl+Alt+Del发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendCtrlAltDel - 发送Ctrl+Alt+Del失败`, error);
      return false;
    }
  }

  /**
   * 获取屏幕截图
   * @param sessionId 会话ID
   */
  async getScreenshot(sessionId: string): Promise<string | null> {
    logger.info(`[RemoteControlService] getScreenshot - 获取截图, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected" || !session.connection) {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 更新最后活动时间
      session.lastActivity = new Date();

      // 发送帧缓冲区更新请求
      // VNC协议: FramebufferUpdateRequest消息格式
      const message = Buffer.alloc(10);
      message.writeUInt8(3, 0); // 消息类型: FramebufferUpdateRequest
      message.writeUInt8(0, 1); // incremental (0 = full update)
      message.writeUInt16BE(0, 2); // x-position
      message.writeUInt16BE(0, 4); // y-position
      message.writeUInt16BE(1024, 6); // width (默认值)
      message.writeUInt16BE(768, 8); // height (默认值)

      session.connection.write(message);

      // 注意: 实际的截图数据会通过VNC协议的FramebufferUpdate消息返回
      // 这里需要监听并处理返回的数据，然后转换为base64格式
      logger.info(`[RemoteControlService] getScreenshot - 截图请求已发送`);

      // 返回占位符，实际实现需要等待VNC服务器响应
      return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    } catch (error) {
      logger.error(`[RemoteControlService] getScreenshot - 获取截图失败`, error);
      throw error;
    }
  }

  /**
   * 获取所有VNC会话
   */
  async getSessions(): Promise<VNCSession[]> {
    logger.info(`[RemoteControlService] getSessions - 获取所有会话`);
    return Array.from(this.sessions.values());
  }

  /**
   * 清理所有会话
   */
  async cleanup(): Promise<void> {
    logger.info(`[RemoteControlService] cleanup - 清理所有会话`);

    for (const [sessionId] of this.sessions) {
      await this.disconnect(sessionId);
    }

    this.sessions.clear();
    this.usedPorts.clear();
  }

  /**
   * 获取会话统计信息
   */
  getSessionStats(): {
    total: number;
    connected: number;
    connecting: number;
    disconnected: number;
    error: number;
  } {
    const stats = {
      total: this.sessions.size,
      connected: 0,
      connecting: 0,
      disconnected: 0,
      error: 0
    };

    for (const session of this.sessions.values()) {
      stats[session.status]++;
    }

    return stats;
  }

  /**
   * 发送文本到远程桌面
   * @param sessionId 会话ID
   * @param text 要发送的文本
   */
  async sendText(sessionId: string, text: string): Promise<boolean> {
    logger.info(`[RemoteControlService] sendText - 发送文本, 会话ID: ${sessionId}, 长度: ${text.length}`);

    const session = this.sessions.get(sessionId);
    if (!session || session.status !== "connected") {
      throw new Error(`会话不存在或未连接: ${sessionId}`);
    }

    try {
      // 将文本转换为键盘事件序列
      for (const char of text) {
        const keyCode = char.charCodeAt(0);
        await this.sendKeyEvent(sessionId, keyCode, true);
        await new Promise(resolve => setTimeout(resolve, 10)); // 短暂延迟
        await this.sendKeyEvent(sessionId, keyCode, false);
      }

      logger.info(`[RemoteControlService] sendText - 文本发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendText - 发送文本失败`, error);
      return false;
    }
  }

  /**
   * 设置会话质量
   * @param sessionId 会话ID
   * @param quality 质量设置
   */
  async setQuality(sessionId: string, quality: "high" | "medium" | "low"): Promise<boolean> {
    logger.info(`[RemoteControlService] setQuality - 设置质量, 会话ID: ${sessionId}, 质量: ${quality}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    try {
      if (session.config) {
        session.config.quality = quality;
      }

      // 这里可以发送VNC协议的编码设置消息来调整质量
      logger.info(`[RemoteControlService] setQuality - 质量设置成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] setQuality - 设置质量失败`, error);
      return false;
    }
  }
}

RemoteControlService.toString = () => "[class RemoteControlService]";

// 导出单例
export const remoteControlService = new RemoteControlService();
export default RemoteControlService;