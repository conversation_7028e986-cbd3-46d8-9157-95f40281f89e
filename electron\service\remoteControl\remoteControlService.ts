"use strict";

import { logger } from "ee-core/log";
import * as net from "net";
import { v4 as uuidv4 } from "uuid";

/**
 * VNC会话接口
 */
interface VNCSession {
  sessionId: string;
  host: string;
  port: number;
  username?: string;
  status: "connecting" | "connected" | "disconnected" | "error";
  createdAt: Date;
  websocketUrl?: string;
  connection?: any;
}

/**
 * 远程控制服务
 * 处理VNC连接和控制逻辑
 * <AUTHOR>
 * @class
 */
class RemoteControlService {
  private sessions: Map<string, VNCSession> = new Map();

  /**
   * 测试VNC连接
   * @param connectionConfig 连接配置
   */
  async testConnection(connectionConfig: {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }): Promise<boolean> {
    return new Promise((resolve) => {
      logger.info(`[RemoteControlService] testConnection - 测试连接到 ${connectionConfig.host}:${connectionConfig.port}`);
      
      const socket = new net.Socket();
      const timeout = 5000; // 5秒超时

      const timer = setTimeout(() => {
        socket.destroy();
        logger.warn(`[RemoteControlService] testConnection - 连接超时: ${connectionConfig.host}:${connectionConfig.port}`);
        resolve(false);
      }, timeout);

      socket.connect(connectionConfig.port, connectionConfig.host, () => {
        clearTimeout(timer);
        socket.destroy();
        logger.info(`[RemoteControlService] testConnection - 连接成功: ${connectionConfig.host}:${connectionConfig.port}`);
        resolve(true);
      });

      socket.on("error", (error) => {
        clearTimeout(timer);
        logger.error(`[RemoteControlService] testConnection - 连接失败: ${connectionConfig.host}:${connectionConfig.port}`, error);
        resolve(false);
      });
    });
  }

  /**
   * 建立VNC连接
   * @param connectionConfig 连接配置
   */
  async connect(connectionConfig: {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }): Promise<VNCSession> {
    logger.info(`[RemoteControlService] connect - 建立VNC连接到 ${connectionConfig.host}:${connectionConfig.port}`);

    const sessionId = uuidv4();
    const session: VNCSession = {
      sessionId,
      host: connectionConfig.host,
      port: connectionConfig.port,
      username: connectionConfig.username,
      status: "connecting",
      createdAt: new Date(),
      websocketUrl: `ws://${connectionConfig.host}:${connectionConfig.port + 1000}` // WebSocket端口通常是VNC端口+1000
    };

    try {
      // 测试连接
      const isConnectable = await this.testConnection(connectionConfig);
      if (!isConnectable) {
        session.status = "error";
        throw new Error(`无法连接到 ${connectionConfig.host}:${connectionConfig.port}`);
      }

      // 这里应该建立实际的VNC连接
      // 由于VNC协议比较复杂，这里先模拟连接成功
      session.status = "connected";
      this.sessions.set(sessionId, session);

      logger.info(`[RemoteControlService] connect - VNC连接建立成功, 会话ID: ${sessionId}`);
      return session;
    } catch (error) {
      session.status = "error";
      this.sessions.set(sessionId, session);
      logger.error(`[RemoteControlService] connect - VNC连接失败`, error);
      throw error;
    }
  }

  /**
   * 断开VNC连接
   * @param sessionId 会话ID
   */
  async disconnect(sessionId: string): Promise<boolean> {
    logger.info(`[RemoteControlService] disconnect - 断开VNC连接, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.warn(`[RemoteControlService] disconnect - 会话不存在: ${sessionId}`);
      return false;
    }

    try {
      // 关闭连接
      if (session.connection) {
        session.connection.destroy();
      }

      session.status = "disconnected";
      this.sessions.delete(sessionId);

      logger.info(`[RemoteControlService] disconnect - VNC连接断开成功, 会话ID: ${sessionId}`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] disconnect - 断开连接失败`, error);
      return false;
    }
  }

  /**
   * 获取连接状态
   * @param sessionId 会话ID（可选，不传则返回所有会话状态）
   */
  async getStatus(sessionId?: string): Promise<VNCSession | VNCSession[]> {
    if (sessionId) {
      logger.info(`[RemoteControlService] getStatus - 获取会话状态, 会话ID: ${sessionId}`);
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`会话不存在: ${sessionId}`);
      }
      return session;
    } else {
      logger.info(`[RemoteControlService] getStatus - 获取所有会话状态`);
      return Array.from(this.sessions.values());
    }
  }

  /**
   * 发送键盘事件
   * @param sessionId 会话ID
   * @param keyCode 键码
   * @param pressed 是否按下
   */
  async sendKeyEvent(sessionId: string, keyCode: number, pressed: boolean): Promise<boolean> {
    logger.info(`[RemoteControlService] sendKeyEvent - 发送键盘事件, 会话ID: ${sessionId}, 键码: ${keyCode}, 按下: ${pressed}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected") {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 这里应该发送实际的VNC键盘事件
      // 目前先模拟发送成功
      logger.info(`[RemoteControlService] sendKeyEvent - 键盘事件发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendKeyEvent - 发送键盘事件失败`, error);
      return false;
    }
  }

  /**
   * 发送鼠标事件
   * @param sessionId 会话ID
   * @param x X坐标
   * @param y Y坐标
   * @param buttonMask 按钮掩码
   */
  async sendMouseEvent(sessionId: string, x: number, y: number, buttonMask: number): Promise<boolean> {
    logger.info(`[RemoteControlService] sendMouseEvent - 发送鼠标事件, 会话ID: ${sessionId}, 坐标: (${x}, ${y}), 按钮: ${buttonMask}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected") {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 这里应该发送实际的VNC鼠标事件
      // 目前先模拟发送成功
      logger.info(`[RemoteControlService] sendMouseEvent - 鼠标事件发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendMouseEvent - 发送鼠标事件失败`, error);
      return false;
    }
  }

  /**
   * 发送Ctrl+Alt+Del
   * @param sessionId 会话ID
   */
  async sendCtrlAltDel(sessionId: string): Promise<boolean> {
    logger.info(`[RemoteControlService] sendCtrlAltDel - 发送Ctrl+Alt+Del, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected") {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 发送Ctrl+Alt+Del组合键
      await this.sendKeyEvent(sessionId, 17, true);  // Ctrl down
      await this.sendKeyEvent(sessionId, 18, true);  // Alt down
      await this.sendKeyEvent(sessionId, 46, true);  // Del down
      
      await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟
      
      await this.sendKeyEvent(sessionId, 46, false); // Del up
      await this.sendKeyEvent(sessionId, 18, false); // Alt up
      await this.sendKeyEvent(sessionId, 17, false); // Ctrl up

      logger.info(`[RemoteControlService] sendCtrlAltDel - Ctrl+Alt+Del发送成功`);
      return true;
    } catch (error) {
      logger.error(`[RemoteControlService] sendCtrlAltDel - 发送Ctrl+Alt+Del失败`, error);
      return false;
    }
  }

  /**
   * 获取屏幕截图
   * @param sessionId 会话ID
   */
  async getScreenshot(sessionId: string): Promise<string | null> {
    logger.info(`[RemoteControlService] getScreenshot - 获取截图, 会话ID: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话不存在: ${sessionId}`);
    }

    if (session.status !== "connected") {
      throw new Error(`会话未连接: ${sessionId}`);
    }

    try {
      // 这里应该获取实际的VNC屏幕截图
      // 目前先返回null，表示功能待实现
      logger.info(`[RemoteControlService] getScreenshot - 截图功能待实现`);
      return null;
    } catch (error) {
      logger.error(`[RemoteControlService] getScreenshot - 获取截图失败`, error);
      throw error;
    }
  }

  /**
   * 获取所有VNC会话
   */
  async getSessions(): Promise<VNCSession[]> {
    logger.info(`[RemoteControlService] getSessions - 获取所有会话`);
    return Array.from(this.sessions.values());
  }

  /**
   * 清理所有会话
   */
  async cleanup(): Promise<void> {
    logger.info(`[RemoteControlService] cleanup - 清理所有会话`);
    
    for (const [sessionId] of this.sessions) {
      await this.disconnect(sessionId);
    }
    
    this.sessions.clear();
  }
}

RemoteControlService.toString = () => "[class RemoteControlService]";

// 导出单例
export const remoteControlService = new RemoteControlService();
export default RemoteControlService;