export class IpUtils {
  /**
   * 将 IPv4 地址转换为 32 位整数
   * @param {string} ip - IPv4 地址（如 "***********"）
   * @returns {number} 转换后的整数
   */
  static ipToNumber(ip) {
    if (!this.validateIp(ip)) throw new Error(ip + ": Invalid IPv4 format");
    const parts = ip.split(".").map(Number);
    const num =
      (parseInt(parts[0], 10) << 24) | (parseInt(parts[1], 10) << 16) | (parseInt(parts[2], 10) << 8) | parseInt(parts[3], 10);
    return num >>> 0;
  }

  /**
   * 将 32 位整数转换为 IPv4 地址
   * @param {number} num - 整数（需在 0-4294967295 范围内）
   * @returns {string} IPv4 地址
   */
  static numberToIp(num) {
    // if (num < 0 || num > 0xffffffff) return num;
    if (num < 0 || num > 0xffffffff) throw new RangeError("Number out of IPv4 range: " + num);
    return [(num >>> 24) & 0xff, (num >>> 16) & 0xff, (num >>> 8) & 0xff, num & 0xff].join(".");
  }

  /**
   * 严格校验 IPv4 格式
   * @param {string} ip - 待校验的 IP 地址
   * @returns {boolean} 校验结果
   */
  static validateIp(ip) {
    const ipv4Regex =
      /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Regex.test(ip);
  }
}
