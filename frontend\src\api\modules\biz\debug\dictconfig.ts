import {
  ProjectDictRequestData,
  ProjectDictRequestRes,
  SetProjectDictRequestData,
  SetProjectDictRes
} from "@/api/interface/biz/debug/dictinfo";
import { moduleIpcRequest } from "@/api/request";

const ipc = moduleIpcRequest("controller/debug/dictconfig/");

/**
 * 词条配置API模块
 */
const dictConfigApi = {
  /** 获取工程词条（指定 deviceId） */
  getProjectDictByDevice(deviceId: string, requestData: ProjectDictRequestData) {
    return ipc.iecInvokeWithDevice<ProjectDictRequestRes>("getProjectDict", requestData, deviceId);
  },
  /** 设置工程词条（指定 deviceId） */
  setProjectDictByDevice(deviceId: string, requestData: SetProjectDictRequestData) {
    return ipc.iecInvokeWithDevice<SetProjectDictRes>("setProjectDict", requestData, deviceId);
  }
};

export { dictConfigApi };
