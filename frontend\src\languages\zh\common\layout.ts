export default {
  language: {
    title: "语言",
    zh: "简体中文",
    en: "英文",
    es: "西班牙语",
    fr: "法语",
    ru: "俄语",
    tooltip: "多语言"
  },
  about: {
    title: "关于",
    introduction: "简介",
    description: "基于 Vue3、TypeScript、Vite4、Pinia、Element-Plus、Electron 等最新技术栈开发的新一代可视化平台调试工具。",
    versionInfo: "版本信息",
    toolName: "工具名称",
    version: "版本号",
    machineCode: "机器码",
    loading: "加载中...",
    machineCodeError: "获取失败",
    copySuccess: "机器码已复制到剪贴板",
    copyError: "复制失败",
    versionFeatures: "版本特征",
    features: {
      visualTool:
        "包含可视化工具连接、装置信息查看、设定量、模拟量、状态量、遥信、遥测、遥控、报告、装置对时、定值导入导出、变量调试功能",
      configTool: "包含组态工具预览、新增、编辑、自定义图符、关联装置信息功能",
      themeTool: "包含主题定制、it小工具、装置配置导入导出功能"
    }
  },
  footer: {
    copyright: "{version}"
  },
  header: {
    minimize: "最小化",
    maximize: "最大化",
    restore: "还原",
    close: "关闭",
    company: {
      name: "思源电气",
      englishName: "Sieyuan"
    },
    collapse: {
      expand: "展开装置",
      fold: "收起装置",
      expandTool: "展开工具列表",
      foldTool: "收起工具列表"
    },
    breadcrumb: {
      home: "首页"
    },
    assemblySize: {
      title: "大小设置",
      default: "默认",
      large: "大型",
      small: "小型"
    },
    avatar: {
      profile: "个人中心",
      switchApp: "切换应用",
      logout: "退出登录",
      logoutConfirm: {
        title: "温馨提示",
        message: "您是否确认退出登录?",
        confirm: "确定",
        cancel: "取消"
      },
      logoutSuccess: "退出登录成功！"
    },
    changeModule: {
      title: "切换模块"
    },
    enginConfig: {
      configType: "配置类型",
      openDirectory: "打开文件目录",
      cancel: "取消",
      confirm: "确定",
      all: "全部",
      deviceList: "装置列表",
      configureList: "组态列表",
      exportSuccess: "导出配置成功",
      importSuccess: "导入配置成功",
      disconnectDeviceFirst: "请先断开已连接的装置",
      overrideConfirm: "已存在组态列表，是否覆盖?",
      warmTips: "温馨提示",
      importConfigFile: "导入配置文件",
      zipFiles: "压缩包文件",
      allFiles: "所有文件"
    },
    userInfo: {
      title: "个人信息",
      cancel: "取消",
      confirm: "确认"
    },
    password: {
      title: "修改密码",
      cancel: "取消",
      confirm: "确认"
    },
    globalSetting: {
      title: "设置",
      tooltip: "设置"
    },
    moreInfo: {
      title: "更多",
      tooltip: "更多",
      items: {
        importConfig: "导入工程配置",
        printScreen: "截图",
        search: "菜单搜索",
        exportConfig: "导出工程配置",
        remoteControl: "远程控制",
        about: "关于",
        help: "帮助"
      },
      importConfig: {
        title: "导入工程配置",
        placeholder: "请选择要导入的配置文件路径"
      },
      exportConfig: {
        title: "导出工程配置",
        placeholder: "请选择导出目录"
      }
    },
    remoteControl: {
      title: "远程控制",
      connectionConfig: "连接配置",
      host: "主机地址",
      hostPlaceholder: "请输入IP地址或主机名",
      hostRequired: "请输入主机地址",
      hostInvalid: "请输入有效的IP地址或主机名",
      port: "端口",
      portRequired: "请输入端口号",
      username: "用户名",
      usernamePlaceholder: "请输入用户名（可选）",
      password: "密码",
      passwordPlaceholder: "请输入密码（可选）",
      connect: "连接",
      disconnect: "断开连接",
      reset: "重置",
      connecting: "正在连接...",
      connected: "已连接",
      disconnected: "已断开连接",
      connectSuccess: "连接成功",
      connectFailed: "连接失败",
      connectError: "连接出错，请检查网络和配置",
      disconnectConfirm: "确定要断开远程连接吗？",
      disconnectSuccess: "断开连接成功",
      warning: "警告",
      confirm: "确定",
      cancel: "取消",
      fullscreen: "全屏",
      screenshot: "截图",
      screenshotTaken: "截图已保存",
      ctrlAltDelSent: "已发送 Ctrl+Alt+Del",
      credentialsRequired: "需要身份验证",
      libraryLoadError: "VNC库加载失败"
    },
    searchMenu: {
      placeholder: "菜单搜索：支持菜单名称、路径",
      empty: "暂无菜单"
    },
    theme: {
      title: "主题",
      tooltip: "主题"
    }
  },
  main: {
    maximize: {
      exit: "退出最大化"
    }
  },
  theme: {
    title: "布局设置",
    quickTheme: {
      title: "主题设置"
    },
    layoutSettings: {
      title: "布局设置"
    },
    layout: {
      title: "布局样式",
      columns: "分栏",
      classic: "经典",
      transverse: "横向",
      vertical: "纵向"
    },
    global: {
      title: "全局主题",
      primary: "主题颜色",
      dark: "暗黑模式",
      grey: "灰色模式",
      weak: "色弱模式",
      special: "特殊模式"
    },
    mode: {
      light: "浅色",
      dark: "深色"
    },
    interface: {
      title: "界面设置",
      watermark: "水印",
      breadcrumb: "面包屑",
      breadcrumbIcon: "面包屑图标",
      tabs: "标签栏",
      tabsIcon: "标签栏图标",
      footer: "页脚",
      drawerForm: "抽屉表单"
    },
    presetThemes: {
      title: "预设主题",
      default: {
        name: "默认主题",
        description: "经典蓝色主题"
      },
      dark: {
        name: "深色主题",
        description: "护眼深色模式"
      },
      techBlue: {
        name: "科技蓝",
        description: "现代科技感蓝色"
      },
      deepBlue: {
        name: "深海蓝",
        description: "深邃稳重蓝色"
      },
      nature: {
        name: "自然主题",
        description: "清新绿色系"
      },
      forestGreen: {
        name: "森林绿",
        description: "深沉森林绿色"
      },
      warm: {
        name: "温暖主题",
        description: "温暖橙色系"
      },
      sunsetOrange: {
        name: "日落橙",
        description: "温暖日落橙色"
      },
      elegant: {
        name: "优雅主题",
        description: "高贵紫色系"
      },
      lavender: {
        name: "薰衣草",
        description: "柔和薰衣草紫"
      },
      sakura: {
        name: "樱花粉",
        description: "浪漫樱花粉色"
      },
      rose: {
        name: "玫瑰红",
        description: "热情玫瑰红色"
      },
      lime: {
        name: "青柠绿",
        description: "活力青柠绿色"
      },
      skyBlue: {
        name: "天空蓝",
        description: "清澈天空蓝色"
      },
      eyeCare: {
        name: "护眼模式",
        description: "灰色护眼主题"
      }
    },
    colors: {
      techBlue: {
        name: "科技蓝",
        description: "现代科技感"
      },
      natureGreen: {
        name: "自然绿",
        description: "清新自然"
      },
      vibrantOrange: {
        name: "活力橙",
        description: "温暖活力"
      },
      elegantPurple: {
        name: "优雅紫",
        description: "高贵优雅"
      },
      romanticPink: {
        name: "浪漫粉",
        description: "温柔浪漫"
      },
      freshCyan: {
        name: "清新青",
        description: "清新淡雅"
      },
      brightYellow: {
        name: "明亮黄",
        description: "明亮活泼"
      },
      warmOrange: {
        name: "温暖橙",
        description: "温暖舒适"
      },
      limeGreen: {
        name: "青柠绿",
        description: "清新青柠"
      },
      deepBlue: {
        name: "深邃蓝",
        description: "深邃稳重"
      },
      golden: {
        name: "金色",
        description: "经典金色"
      },
      chinaRed: {
        name: "中国红",
        description: "传统红色"
      }
    }
  },
  tabs: {
    moreButton: {
      refresh: "刷新",
      closeCurrent: "关闭当前",
      closeLeft: "关闭左侧",
      closeRight: "关闭右侧",
      closeOthers: "关闭其它",
      closeAll: "关闭所有"
    }
  }
};
