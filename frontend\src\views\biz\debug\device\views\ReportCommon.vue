<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="sameSearch">
      <svg-icon icon="ant-design:search-outlined" class="menu-svg" />
      <span>{{ t("report.sameSearch") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-item @click="sameFilter">
      <svg-icon icon="ant-design:filter-outlined" class="menu-svg" />
      <span>{{ t("report.sameFilter") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="dealTimeCol">
      <svg-icon icon="ant-design:field-time-outlined" class="menu-svg" />
      <span>{{ t("report.showHideTime") }}</span>
    </v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          {{ t("report.searchType") }}：
          <el-select v-model="searchType" style="width: 80px">
            <el-option v-for="item in searchTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchInfo" clearable style="width: 150px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{
          t("report.search")
        }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{
          t("report.save")
        }}</el-button>
        <el-button type="primary" plain :icon="Refresh" @click="refreshReport" :loading="refreshLoading">{{
          refreshName
        }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="table-box report-table" v-contextmenu:contextmenu="contextmenu">
      <ProTable
        ref="proTable"
        :data="filterTableData"
        :columns="proTableColumns"
        :pagination="true"
        :tool-button="false"
        :border="true"
        :stripe="true"
        :loading="globalReport?.isReportLoading || false"
        :pager-count="7"
        :request-auto="false"
        row-key="entryID"
        @cell-contextmenu="cellContextmenu"
      >
        <template #tableHeader>
          <span></span>
        </template>
      </ProTable>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Refresh, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { getDateZh } from "@/utils/index";
import { ipc } from "@/api/request/ipcRenderer";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { IECNotify, RealEventState, ReportParam, ResultData } from "@/api";
import { filter, includes } from "lodash";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { useDebugStore } from "@/stores/modules/debug";
import { reportApi } from "@/api/modules/biz/debug/report";
import { realEventApi } from "@/api/modules/biz/debug/realevent";
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { osControlApi } from "@/api/modules/biz/os/control";
import { useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
const progressDialog = ref();

const { t } = useI18n();

const realEventState = ref<RealEventState>({
  subscribe: false,
  type: ""
});
const refreshLoading = ref(false);
const { paramInfo } = useConfigStore();
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref(false);
const showTime = ref(true);
const contextmenu = ref<ContextmenuInstance>();
let currLine: any = undefined;
const cellContextmenu = (row): void => {
  currLine = row;
};
const searchInfo = ref("");
const searchType = ref(0);
const searchTypes = computed(() => [
  {
    value: 0,
    label: t("report.search")
  },
  {
    value: 1,
    label: t("report.filter")
  }
]);

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange:
      dateRange.value && dateRange.value.length >= 2
        ? [new Date(dateRange.value[0]), new Date(dateRange.value[1])]
        : getDefaultDateRange(),
    searchInfo: searchInfo.value,
    searchType: searchType.value
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
    searchInfo.value = cached.searchInfo;
    searchType.value = cached.searchType;
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
    searchInfo.value = "";
    searchType.value = 0;
  }
};
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: ""
});
const tableData = ref<any[]>([]);
const refreshMark = ref<boolean>(false);
const refreshName = computed(() => (refreshMark.value ? t("report.stopRefresh") : t("report.autoRefresh")));
const totalNum = computed(() => {
  return filterTableData.value.length;
});
const filterTableData = computed(() => {
  // 如果 globalReport!.keyword不为空，按照这个关键子先过滤
  if (searchType.value == 1 && searchInfo.value) {
    return filter(tableData.value, item => includes(item.name.toLowerCase(), searchInfo.value.toLowerCase()));
  }
  return tableData.value;
});

const highLightData = (desc: string): string => {
  if (searchType.value == 0 && searchInfo.value && desc) {
    try {
      // 转义正则表达式特殊字符
      const escapedSearchInfo = searchInfo.value.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const regex = new RegExp(`(${escapedSearchInfo})`, "gi");
      return desc.replace(regex, '<span style="color: var(--el-color-primary);font-weight: bold;">$&</span>');
    } catch (error) {
      // 如果正则表达式创建失败，返回原始描述
      console.warn("正则表达式创建失败:", error);
      return desc;
    }
  }
  return desc || "";
};

// ProTable列配置
const proTableColumns: ColumnProps<ReportParam.IECRpcCommonReportRes>[] = [
  { prop: "entryID", label: t("report.reportNumber"), width: 100 },
  {
    prop: "time",
    label: t("report.time"),
    width: 300,
    isShow: computed(() => showTime.value)
  },
  {
    prop: "name",
    label: t("report.description"),
    render: scope => {
      return h("span", { innerHTML: highLightData(scope.row.name) });
    }
  }
];

const proTable = ref();
//  QueryHisEvtByTime: ReportCommon,
//  QueryHisFaultByTime: ReportGroup,
//  QueryOpReportByTime: ReportOperate,
//  QueryAuditLogByTime: ReportCommon
const getTableData = (): ReportParam.IECRpcCommonReportRes[] => {
  switch (globalReport!.currReportMethod) {
    case "QueryHisEvtByTime":
      return globalReport!.commonReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
    default:
      return [];
  }
};

let timerId;
const timerRefresh = (): void => {
  timerId = setTimeout(() => {
    refreshList().then(() => {
      timerRefresh();
    });
  }, paramInfo.REPORT_REFRESH_TIME);
};

const sameSearch = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name || "";
  if (sameDesc && sameDesc.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 0;
  searchInfo.value = sameDesc;
};
const sameFilter = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name || "";
  if (sameDesc && sameDesc.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 1;
  searchInfo.value = sameDesc;
};
const dealTimeCol = (): void => {
  showTime.value = !showTime.value;
};
const refreshReport = (): void => {
  refreshMark.value = !refreshMark.value;
  if (refreshMark.value == true) {
    subRealEvent();
  } else {
    unSubRealEvent();
  }
};

const clearList = (): void => {
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[0])) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[1])) : "",
    entryAfter: "",
    orderBy: "DESC"
  };
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  globalReport!.isReportLoading = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("report.searchProgress", { type: reportDesc });
  startFakeProgress();
  // 保证进度条弹窗至少显示500ms
  const minShowPromise = new Promise(resolve => setTimeout(resolve, 500));
  const searchPromise = reportApi.getCommonReportListByDevice(props.deviceId, arg);
  const [res] = await Promise.all([searchPromise, minShowPromise]);
  stopFakeProgress();
  dialogShow.value.searchProgress = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    globalReport!.isReportLoading = false;
    isButtonClick.value = false;
    return;
  }
};

const refreshList = async (): Promise<void> => {
  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: "",
    stopTime: "",
    orderBy: "DESC"
  };
  const res = await reportApi.refreshReportByDevice(props.deviceId, arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }
  if (Array.isArray(res.data)) {
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", res.data);
    let filteredList = res.data as any;
    if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
      filteredList = res.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
    }
    tableData.value = filteredList;
  } else {
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    tableData.value = [];
  }
};

let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.value.percentage = 0;
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.value.percentage < 95) {
      dialogShow.value.percentage += 5;
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.value.percentage = 100;
}
const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  progressDialog.value.setProgress(5, t("report.exporting"), false);
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      name: obj.name,
      time: obj.time
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  }).catch(() => {
    // 用户点击关闭按钮取消操作，不需要处理
  });
};
const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;
  if (notify.type == "readCommonReport") {
    console.log(
      `[CommonReport UI] 处理通用报告通知 - isPartial: ${notify.isPartial}, 数据类型: ${reportData.data?.progress ? "进度" : "数据"}, 当前界面状态: isReportLoading=${globalReport!.isReportLoading}, searchProgress=${dialogShow.value.searchProgress}`
    );

    if (reportData.code != 1) {
      // 错误处理
      globalReport!.isReportLoading = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
      Message.warning(reportData.msg);
      console.log(`[CommonReport UI] 处理错误完成`);
      return;
    }

    // 检查是否为进度更新
    if (reportData.data?.progress?.isProgress) {
      // 这是进度更新，只更新进度条，不更新表格数据
      const progressInfo = reportData.data.progress;
      const currentCount = progressInfo.currentCount;
      const callbackCount = progressInfo.callbackCount;

      // 基于实际数据量计算进度
      let progressPercent = 0;
      if (currentCount > 0) {
        // 使用对数函数计算进度，避免进度跳跃过快
        progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
      } else {
        progressPercent = Math.min(20, callbackCount * 3);
      }

      // 确保进度不倒退
      progressPercent = Math.max(dialogShow.value.percentage, progressPercent);

      dialogShow.value.percentage = progressPercent;

      console.log(
        `[CommonReport UI] 进度更新完成 - 数据量: ${currentCount}条, 回调次数: ${callbackCount}, 进度: ${progressPercent}%`
      );
      return;
    }

    // 这是最终数据更新
    if (Array.isArray(reportData.data)) {
      console.log(`[CommonReport UI] 处理最终数据 - 原始数据量: ${reportData.data.length}条`);

      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        filteredList = reportData.data.filter(item =>
          (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase())
        );
        console.log(`[CommonReport UI] 数据过滤完成 - 原始数据: ${reportData.data.length}条, 过滤后: ${filteredList.length}条`);
      }

      // 最终数据直接渲染到表格
      tableData.value = filteredList;
      globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

      // 完成加载
      globalReport!.isReportLoading = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[CommonReport UI] 最终数据处理完成 - 最终数据量: ${filteredList.length}条`);
    } else {
      // 空数据情况
      console.log(`[CommonReport UI] 处理空数据情况`);
      globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];

      globalReport!.isReportLoading = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[CommonReport UI] 空数据处理完成`);
    }
  }
};

onMounted(() => {
  ipc.on("report_notify", notifyMethod);
  console.log("ReportCommon onMounted:", {
    newname: globalReport?.newname,
    currReport极Type: globalReport?.currReportType,
    currReportMethod: globalReport?.currReportMethod,
    dataLength: getTableData().length
  });
  tableData.value = getTableData();
  // 恢复查询条件
  restoreQueryConditions();
});

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  ipc.removeAllListeners("report_notify");
});

watch(
  () => refreshMark.value,
  newValue => {
    if (newValue == true) {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = true;
      timerRefresh();
    } else {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = false;
      if (timerId) {
        clearTimeout(timerId);
      }
    }
  }
);
watch(
  () => globalReport!.newname,
  (newValue, oldValue) => {
    console.log("ReportCommon watch触发:", {
      newValue,
      oldValue,
      currReportType: globalReport!.currReportType,
      currReportMethod: globalReport!.currReportMethod
    });
    tableData.value = getTableData();
    isButtonClick.value = false;
    refreshMark.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();

    // refreshName现在是computed属性，会自动更新
    if (timerId) {
      clearTimeout(timerId);
    }
    unSubRealEvent();
  }
);

// 语言切换时refreshName会自动更新，不需要手动监听
// watch(
//   () => locale.value,
//   () => {
//     // refreshName现在是computed属性，会自动响应语言变化
//   }
// );

const subRealEvent = async () => {
  if (realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 订阅事件
    const res = await realEventApi.subRealEventByDevice(props.deviceId, [globalReport!.currReportType]);
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = true;
      realEventState.value.type = globalReport!.currReportType;
    }
  } finally {
    refreshLoading.value = false;
  }
};
const unSubRealEvent = async () => {
  if (!realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 取消订阅
    const res = await realEventApi.unSubRealEventByDevice(props.deviceId, { type: [realEventState.value.type] });
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = false;
      realEventState.value.type = "";
    }
  } finally {
    refreshLoading.value = false;
  }
};
onUnmounted(() => {
  unSubRealEvent();
});
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  margin-top: 5px;
  .el-row {
    div {
      font-size: 14px;
      :deep(.el-date-editor) {
        width: 340px;
      }
    }
  }
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin: 8px 0;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
  }
  .report-table {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;

    .pagination-container {
      padding: 10px;
      display: flex;
      justify-content: center;
      background: #fff;
      border-top: 1px solid #ebeef5;
    }
  }
}
.header {
  margin-bottom: 5px;
}
</style>
