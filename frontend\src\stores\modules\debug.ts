import { defineStore } from "pinia";
import { DebugIndex, DebugDeviceInfo, GlobalState, DebugReportInfo, ReportQueryCondition } from "@/stores/interface";
import { isEmpty } from "lodash";
import { getDateTimeFormat } from "@/utils";
import { deviceOperateApi, ReportParam, ResultData } from "@/api";
import { ERROR_CODES } from "@/api/interface/biz/debug/errorCodes";

const name = "simple-debug"; // 定义模块名称
/**  DictState */
export interface DebugState {
  /** 调试工具基本信息 */
  debugIndex: DebugIndex;
  currDevice: DebugDeviceInfo;
  searchDevice: string;
  deviceList: DebugDeviceInfo[];
  report: Map<string, DebugReportInfo>;
  consoleLog: string[];
}

/** 调试模块 */
const debugStoreOptions = {
  id: name,
  state: (): DebugState => {
    return {
      debugIndex: {
        asideIndex: "1",
        currentComponent: new Map(),
        compName: "装置信息",
        compData: new Map()
      },
      currDevice: {
        id: "",
        ip: "",
        name: "",
        port: "",
        encrypted: false,
        prjType: undefined as unknown as number,
        deviceType: undefined as unknown as number,
        isConnect: false,
        isActive: false,
        connectTime: ""
      },
      searchDevice: "",
      deviceList: [],
      report: new Map(),
      consoleLog: []
    };
  },
  getters: {
    debugIndexGet: state => state.debugIndex
  },
  actions: {
    setDebugState(...args: ObjToKeyValArray<GlobalState>) {
      this.$patch({ [args[0]]: args[1] });
    },
    setSearchDevice(value: string) {
      this.searchDevice = value;
    },
    // 设置当前设备
    setCurrDevice(device: DebugDeviceInfo) {
      this.currDevice = device;
    },
    // 添加设备
    async addDevice(device: DebugDeviceInfo) {
      const res: ResultData = await deviceOperateApi.addDeviceCfg(device);
      if (res.code == ERROR_CODES.SUCCESS) {
        this.deviceList.push(res.data);
        this.addConsole("装置 " + device.name + "：添加成功");
      }
    },
    // 更新设备
    async updateDevice(device: DebugDeviceInfo) {
      const res: ResultData = await deviceOperateApi.updateDeviceCfg(device.id, device);
      if (res.code == ERROR_CODES.SUCCESS) {
        const index = this.deviceList.findIndex(item => item.id === device.id);
        if (index >= ERROR_CODES.SUCCESS) {
          this.deviceList.splice(index, 1, device);
          this.addConsole("装置 " + device.name + "：更新完成");
        }
      } else {
        this.addConsole(res.msg);
      }
    },
    // 删除设备
    async removeDevice(id: string) {
      const res: ResultData = await deviceOperateApi.removeDeviceCfg(id);
      if (res.code == ERROR_CODES.SUCCESS) {
        const index = this.deviceList.findIndex(item => item.id === id);
        if (index >= 0) {
          const removceDevice = this.deviceList[index];
          this.addConsole("装置 " + removceDevice.name + "：删除完成");
          this.deviceList.splice(index, 1);
        }
      } else {
        this.addConsole(res.msg);
      }
    },
    // 断开所有设备连接
    disconnectAllDevices() {
      this.deviceList.forEach(device => {
        if (device.isConnect) {
          device.isConnect = false;
          this.addConsole(`装置 ${device.name}：已断开连接`);
        }
      });
    },
    // 初始化报告数据
    initReportData(id: string) {
      const reportObj: DebugReportInfo = {
        currReportType: "",
        currReportMethod: "",
        currReportDesc: "",
        newname: "", // 初始化newname属性
        keyword: "", // 初始化keyword属性
        isReportLoading: false,
        commonReport: new Map<string, ReportParam.IECRpcCommonReportRes[]>(),
        operateReport: new Map<string, ReportParam.IECRpcOperateReportRes[]>(),
        groupReport: new Map<string, ReportParam.IECRpcGroupReportRes[]>(),
        auditlogReport: new Map<string, ReportParam.IECRpcOperateReportRes[]>(),
        queryConditions: new Map<string, ReportQueryCondition>()
      };
      this.report.set(id, reportObj);
    },
    // 移除报告数据缓存
    removeReportData(id: string) {
      this.report.delete(id);
    },
    // 控制台打印
    addConsole(msg: string) {
      if (isEmpty(msg)) {
        return;
      }
      const appendStr = getDateTimeFormat() + "：" + msg;
      if (this.consoleLog.length > 10000) {
        this.consoleLog?.shift();
      }
      console.log(appendStr);
      this.consoleLog.push(appendStr);
    },
    // 清空控制台
    clearConsole() {
      this.consoleLog.splice(0, this.consoleLog.length);
    }
  }
};

// 共享存储（单例模式，所有组件共用）
export const useDebugStore = defineStore(name, debugStoreOptions);

// 独立存储工厂函数，每个组件可创建独立实例
export const createScopeDebugStore = (scopeid: string) => {
  return defineStore(scopeid, debugStoreOptions);
};
