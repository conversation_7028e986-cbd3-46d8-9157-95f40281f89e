import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/sys/remote/");

export namespace RemoteControl {
  export interface ConnectionConfig {
    host: string;
    port: number;
    username?: string;
    password?: string;
  }

  export interface ConnectionStatus {
    connected: boolean;
    host?: string;
    port?: number;
    connectedAt?: string;
  }

  export interface VNCSession {
    sessionId: string;
    host: string;
    port: number;
    status: "connecting" | "connected" | "disconnected" | "error";
    createdAt: string;
    lastActivity?: string;
    websocketUrl?: string;
  }
}

// 远程控制相关 API
const remoteControlApi = {
  // 测试VNC连接
  testConnection(params: RemoteControl.ConnectionConfig) {
    console.log("remoteControlApi.testConnection");
    return ipc.invoke<boolean>("testConnection", params);
  },

  // 建立VNC连接
  connect(params: RemoteControl.ConnectionConfig) {
    console.log("remoteControlApi.connect");
    return ipc.invoke<RemoteControl.VNCSession>("connect", params);
  },

  // 断开VNC连接
  disconnect(sessionId: string) {
    console.log("remoteControlApi.disconnect");
    return ipc.invoke<boolean>("disconnect", { sessionId });
  },

  // 获取连接状态
  getStatus(sessionId?: string) {
    console.log("remoteControlApi.getStatus");
    return ipc.invoke<RemoteControl.ConnectionStatus>("getStatus", { sessionId });
  },

  // 发送键盘事件
  sendKeyEvent(sessionId: string, keyCode: number, pressed: boolean) {
    console.log("remoteControlApi.sendKeyEvent");
    return ipc.invoke<boolean>("sendKeyEvent", {
      sessionId,
      keyCode,
      pressed
    });
  },

  // 发送鼠标事件
  sendMouseEvent(sessionId: string, x: number, y: number, buttonMask: number) {
    console.log("remoteControlApi.sendMouseEvent");
    return ipc.invoke<boolean>("sendMouseEvent", {
      sessionId,
      x,
      y,
      buttonMask
    });
  },

  // 发送Ctrl+Alt+Del
  sendCtrlAltDel(sessionId: string) {
    console.log("remoteControlApi.sendCtrlAltDel");
    return ipc.invoke<boolean>("sendCtrlAltDel", { sessionId });
  },

  // 获取屏幕截图
  getScreenshot(sessionId: string) {
    console.log("remoteControlApi.getScreenshot");
    return ipc.invoke<string>("getScreenshot", { sessionId });
  },

  // 获取VNC会话列表
  getSessions() {
    console.log("remoteControlApi.getSessions");
    return ipc.invoke<RemoteControl.VNCSession[]>("getSessions");
  }
};

export { remoteControlApi };
